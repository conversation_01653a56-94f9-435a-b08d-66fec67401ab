import React from 'react';
import styled, { keyframes } from 'styled-components';

interface TypingIndicatorProps {
  isVisible: boolean;
}

const bounce = keyframes`
  0%, 80%, 100% { 
    transform: translateY(0);
  }
  40% { 
    transform: translateY(-5px);
  }
`;

const TypingIndicatorContainer = styled.div<{ isVisible: boolean }>`
  display: ${({ isVisible }) => (isVisible ? 'flex' : 'none')};
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  max-width: 80%;
  background-color: #f0f0f0;
  border-radius: 18px;
`;

const Dot = styled.div`
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: #888;
  border-radius: 50%;
  animation: ${bounce} 1.4s infinite ease-in-out both;
  
  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
`;

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ isVisible }) => {
  return (
    <TypingIndicatorContainer isVisible={isVisible} aria-hidden={!isVisible}>
      <Dot />
      <Dot />
      <Dot />
    </TypingIndicatorContainer>
  );
};

export default TypingIndicator;
