#!/usr/bin/env ts-node

/**
 * FAQ Data Validation Tool
 * 
 * This script validates the quality and structure of FAQ data from Excel files,
 * providing detailed statistics and identifying potential issues.
 * 
 * Usage: npx ts-node scripts/validate-faq-data.ts
 */

import { dataProcessor } from '../src/indexing/data-processor';
import { logger } from '../src/utils/logger';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.test') });

async function validateFaqData() {
  try {
    logger.info('📊 FAQ Data Validation');
    logger.info('======================');

    // Test 1: Process Excel Data
    logger.info('\n📋 Test 1: Excel Data Processing');
    logger.info('================================');
    
    const result = await dataProcessor.processExcelToFaqEntries();
    
    logger.info('✅ Processing Results:');
    logger.info(`   Total Processed: ${result.totalProcessed}`);
    logger.info(`   Successful: ${result.successful}`);
    logger.info(`   Failed: ${result.failed}`);
    logger.info(`   Success Rate: ${((result.successful / result.totalProcessed) * 100).toFixed(1)}%`);

    if (result.failed > 0) {
      logger.info('\n⚠️ Processing Errors:');
      result.errors.forEach((error, index) => {
        logger.info(`   ${index + 1}. ${error.error} (Row: ${error.rowIndex})`);
      });
    }

    // Test 2: Data Quality Statistics
    logger.info('\n📊 Test 2: Data Quality Statistics');
    logger.info('==================================');
    
    logger.info('✅ Content Statistics:');
    logger.info(`   Average Question Length: ${result.statistics.averageQuestionLength} characters`);
    logger.info(`   Average Answer Length: ${result.statistics.averageAnswerLength} characters`);
    logger.info(`   Categories Found: ${result.statistics.categoriesCount}`);
    logger.info(`   Categories: ${result.statistics.categories.join(', ')}`);

    // Test 3: FAQ Entry Validation
    logger.info('\n🔍 Test 3: FAQ Entry Validation');
    logger.info('===============================');
    
    const faqEntries = await dataProcessor.convertToFaqEntries();
    logger.info(`✅ FAQ Entries Generated: ${faqEntries.length}`);

    // Validate entry structure
    let validEntries = 0;
    let invalidEntries = 0;
    const issues: string[] = [];

    faqEntries.forEach((entry, index) => {
      let isValid = true;
      
      if (!entry.id || typeof entry.id !== 'string') {
        issues.push(`Entry ${index + 1}: Missing or invalid ID`);
        isValid = false;
      }
      
      if (!entry.question || entry.question.length < 10) {
        issues.push(`Entry ${index + 1}: Question too short (${entry.question?.length || 0} chars)`);
        isValid = false;
      }
      
      if (!entry.answer || entry.answer.length < 20) {
        issues.push(`Entry ${index + 1}: Answer too short (${entry.answer?.length || 0} chars)`);
        isValid = false;
      }
      
      if (!entry.category || entry.category.length === 0) {
        issues.push(`Entry ${index + 1}: Missing category`);
        isValid = false;
      }

      if (isValid) {
        validEntries++;
      } else {
        invalidEntries++;
      }
    });

    logger.info(`✅ Valid Entries: ${validEntries}`);
    logger.info(`❌ Invalid Entries: ${invalidEntries}`);
    
    if (issues.length > 0) {
      logger.info('\n⚠️ Data Quality Issues:');
      issues.slice(0, 10).forEach(issue => {
        logger.info(`   • ${issue}`);
      });
      if (issues.length > 10) {
        logger.info(`   ... and ${issues.length - 10} more issues`);
      }
    }

    // Test 4: Category Distribution
    logger.info('\n📈 Test 4: Category Distribution');
    logger.info('================================');
    
    const categoryCount: Record<string, number> = {};
    faqEntries.forEach(entry => {
      categoryCount[entry.category] = (categoryCount[entry.category] || 0) + 1;
    });

    logger.info('✅ Category Distribution:');
    Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        const percentage = ((count / faqEntries.length) * 100).toFixed(1);
        logger.info(`   ${category}: ${count} entries (${percentage}%)`);
      });

    // Test 5: Content Length Analysis
    logger.info('\n📏 Test 5: Content Length Analysis');
    logger.info('==================================');
    
    const questionLengths = faqEntries.map(e => e.question.length);
    const answerLengths = faqEntries.map(e => e.answer.length);
    
    const questionStats = {
      min: Math.min(...questionLengths),
      max: Math.max(...questionLengths),
      avg: Math.round(questionLengths.reduce((sum, len) => sum + len, 0) / questionLengths.length),
    };
    
    const answerStats = {
      min: Math.min(...answerLengths),
      max: Math.max(...answerLengths),
      avg: Math.round(answerLengths.reduce((sum, len) => sum + len, 0) / answerLengths.length),
    };

    logger.info('✅ Question Length Statistics:');
    logger.info(`   Min: ${questionStats.min} chars`);
    logger.info(`   Max: ${questionStats.max} chars`);
    logger.info(`   Average: ${questionStats.avg} chars`);
    
    logger.info('✅ Answer Length Statistics:');
    logger.info(`   Min: ${answerStats.min} chars`);
    logger.info(`   Max: ${answerStats.max} chars`);
    logger.info(`   Average: ${answerStats.avg} chars`);

    // Test 6: Processed Documents Validation
    logger.info('\n📄 Test 6: Processed Documents Validation');
    logger.info('=========================================');
    
    const processedDocs = await dataProcessor.convertToProcessedDocuments(faqEntries);
    logger.info(`✅ Processed Documents: ${processedDocs.length}`);

    // Validate document structure
    let validDocs = 0;
    processedDocs.forEach((doc, index) => {
      if (doc.id && doc.content && doc.metadata && 
          doc.metadata.question && doc.metadata.answer && doc.metadata.category) {
        validDocs++;
      } else {
        logger.warn(`   ⚠️ Document ${index + 1}: Invalid structure`);
      }
    });

    logger.info(`✅ Valid Documents: ${validDocs}/${processedDocs.length}`);

    // Overall Assessment
    logger.info('\n🎯 OVERALL ASSESSMENT');
    logger.info('====================');
    
    const successRate = (result.successful / result.totalProcessed) * 100;
    const validityRate = (validEntries / faqEntries.length) * 100;
    
    if (successRate >= 90 && validityRate >= 90) {
      logger.info('✅ EXCELLENT - Data quality is production-ready');
    } else if (successRate >= 80 && validityRate >= 80) {
      logger.info('✅ GOOD - Data quality is acceptable with minor issues');
    } else if (successRate >= 70 && validityRate >= 70) {
      logger.info('⚠️ FAIR - Data quality needs improvement');
    } else {
      logger.info('❌ POOR - Data quality requires significant work');
    }

    logger.info('\n💡 RECOMMENDATIONS');
    logger.info('==================');
    
    if (successRate < 90) {
      logger.info('   • Review Excel file format and structure');
      logger.info('   • Check for missing or malformed data');
      logger.info('   • Validate column headers and data types');
    }
    
    if (validityRate < 90) {
      logger.info('   • Review content quality guidelines');
      logger.info('   • Ensure minimum content length requirements');
      logger.info('   • Validate category assignments');
    }
    
    if (Object.keys(categoryCount).length < 3) {
      logger.info('   • Consider adding more diverse FAQ categories');
      logger.info('   • Review categorization strategy');
    }

    logger.info('\n🎉 FAQ Data Validation Complete!');

  } catch (error) {
    logger.error('❌ FAQ data validation failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  validateFaqData();
}
