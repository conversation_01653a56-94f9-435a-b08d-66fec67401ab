import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import { RelevantFaqResult } from '../../rag/interfaces/rag.interface';

/**
 * Main AI orchestrator service that coordinates between RAG and MCP functionalities
 */
export interface IAiOrchestratorService {
  /**
   * Process user query and determine the appropriate response strategy
   * @param query User query
   * @param conversationHistory Previous conversation messages
   * @param availableTools Available MCP tools
   */
  processQuery(
    query: string,
    conversationHistory: ChatMessage[],
    availableTools?: Tool[]
  ): Promise<AiResponse>;

  /**
   * Handle tool execution requests
   * @param toolCalls Array of tool calls to execute
   */
  executeTools(toolCalls: ToolCall[]): Promise<ToolExecutionResult[]>;

  /**
   * Check service availability
   */
  isAvailable(): Promise<boolean>;
}

/**
 * AI response with context about how it was generated
 */
export interface AiResponse {
  content: string;
  responseType: ResponseType;
  ragContext?: RelevantFaqResult[];
  toolCalls?: ToolCall[];
  confidence?: number;
  metadata?: Record<string, any>;
}

/**
 * Types of AI responses
 */
export enum ResponseType {
  RAG_BASED = 'rag_based',           // Response generated using RAG context
  DIRECT_LLM = 'direct_llm',         // Direct LLM response without RAG
  TOOL_CALLING = 'tool_calling',     // Response requires tool execution
  HYBRID = 'hybrid',                 // Combination of RAG and tool calling
}

/**
 * Result of tool execution
 */
export interface ToolExecutionResult {
  toolCallId: string;
  success: boolean;
  result?: any;
  error?: string;
  executionTime?: number;
}

/**
 * Query analysis result
 */
export interface QueryAnalysis {
  intent: QueryIntent;
  requiresRag: boolean;
  requiresTools: boolean;
  suggestedTools?: string[];
  confidence: number;
  keywords?: string[];
}

/**
 * Types of query intents
 */
export enum QueryIntent {
  INFORMATION_SEEKING = 'information_seeking',   // User wants information from FAQ
  TASK_EXECUTION = 'task_execution',             // User wants to perform an action
  CONVERSATIONAL = 'conversational',            // General conversation
  TROUBLESHOOTING = 'troubleshooting',          // Technical support
  MIXED = 'mixed',                              // Multiple intents
}

/**
 * Configuration for AI orchestrator
 */
export interface AiOrchestratorConfig {
  ragEnabled: boolean;
  mcpEnabled: boolean;
  ragThreshold: number;           // Minimum similarity score for RAG results
  maxRagResults: number;          // Maximum number of RAG results to include
  toolExecutionTimeout: number;   // Timeout for tool execution
  hybridModeEnabled: boolean;     // Allow combining RAG and tool calling
}

/**
 * Interface for query intent analyzer
 */
export interface IQueryIntentAnalyzer {
  /**
   * Analyze user query to determine intent and requirements
   * @param query User query
   * @param conversationHistory Previous conversation context
   */
  analyzeQuery(query: string, conversationHistory: ChatMessage[]): Promise<QueryAnalysis>;

  /**
   * Check if query is likely to benefit from RAG
   * @param query User query
   */
  shouldUseRag(query: string): Promise<boolean>;

  /**
   * Suggest relevant tools for a query
   * @param query User query
   * @param availableTools Available tools
   */
  suggestTools(query: string, availableTools: Tool[]): Promise<string[]>;
}

/**
 * Interface for response generator that combines different sources
 */
export interface IResponseGenerator {
  /**
   * Generate response using RAG context
   * @param query User query
   * @param ragResults Relevant FAQ results
   * @param conversationHistory Previous messages
   */
  generateRagResponse(
    query: string,
    ragResults: RelevantFaqResult[],
    conversationHistory: ChatMessage[]
  ): Promise<string>;

  /**
   * Generate direct LLM response
   * @param query User query
   * @param conversationHistory Previous messages
   */
  generateDirectResponse(
    query: string,
    conversationHistory: ChatMessage[]
  ): Promise<string>;

  /**
   * Generate response that incorporates tool execution results
   * @param query User query
   * @param toolResults Results from tool execution
   * @param conversationHistory Previous messages
   */
  generateToolBasedResponse(
    query: string,
    toolResults: ToolExecutionResult[],
    conversationHistory: ChatMessage[]
  ): Promise<string>;

  /**
   * Generate hybrid response combining RAG and tool results
   * @param query User query
   * @param ragResults Relevant FAQ results
   * @param toolResults Results from tool execution
   * @param conversationHistory Previous messages
   */
  generateHybridResponse(
    query: string,
    ragResults: RelevantFaqResult[],
    toolResults: ToolExecutionResult[],
    conversationHistory: ChatMessage[]
  ): Promise<string>;
}

/**
 * Service metrics and monitoring
 */
export interface AiOrchestratorMetrics {
  totalQueries: number;
  ragQueries: number;
  toolQueries: number;
  hybridQueries: number;
  averageResponseTime: number;
  successRate: number;
  ragHitRate: number;
  toolSuccessRate: number;
}

/**
 * Interface for metrics collection
 */
export interface IAiMetricsCollector {
  /**
   * Record query processing metrics
   */
  recordQuery(
    responseType: ResponseType,
    responseTime: number,
    success: boolean
  ): void;

  /**
   * Get current metrics
   */
  getMetrics(): AiOrchestratorMetrics;

  /**
   * Reset metrics
   */
  resetMetrics(): void;
}
