/**
 * Base class for vLLM provider errors
 */
export abstract class VllmError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  readonly timestamp: string;
  readonly context?: Record<string, any>;

  constructor(message: string, context?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date().toISOString();
    this.context = context;
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      timestamp: this.timestamp,
      context: this.context,
    };
  }
}

/**
 * Configuration-related errors
 */
export class VllmConfigurationError extends VllmError {
  readonly code = 'VLLM_CONFIG_ERROR';
  readonly statusCode = 500;

  constructor(message: string, context?: Record<string, any>) {
    super(`vLLM Configuration Error: ${message}`, context);
  }
}

/**
 * Network connectivity errors
 */
export class VllmNetworkError extends VllmError {
  readonly code = 'VLLM_NETWORK_ERROR';
  readonly statusCode = 503;

  constructor(message: string, context?: Record<string, any>) {
    super(`vLLM Network Error: ${message}`, context);
  }
}

/**
 * API authentication errors
 */
export class VllmAuthenticationError extends VllmError {
  readonly code = 'VLLM_AUTH_ERROR';
  readonly statusCode = 401;

  constructor(message: string, context?: Record<string, any>) {
    super(`vLLM Authentication Error: ${message}`, context);
  }
}

/**
 * API rate limiting errors
 */
export class VllmRateLimitError extends VllmError {
  readonly code = 'VLLM_RATE_LIMIT_ERROR';
  readonly statusCode = 429;
  readonly retryAfter?: number;

  constructor(message: string, retryAfter?: number, context?: Record<string, any>) {
    super(`vLLM Rate Limit Error: ${message}`, context);
    this.retryAfter = retryAfter;
  }
}

/**
 * API request timeout errors
 */
export class VllmTimeoutError extends VllmError {
  readonly code = 'VLLM_TIMEOUT_ERROR';
  readonly statusCode = 408;
  readonly timeoutMs: number;

  constructor(timeoutMs: number, context?: Record<string, any>) {
    super(`vLLM request timed out after ${timeoutMs}ms`, context);
    this.timeoutMs = timeoutMs;
  }
}

/**
 * API response parsing errors
 */
export class VllmResponseParsingError extends VllmError {
  readonly code = 'VLLM_RESPONSE_PARSING_ERROR';
  readonly statusCode = 502;
  readonly rawResponse?: string;

  constructor(message: string, rawResponse?: string, context?: Record<string, any>) {
    super(`vLLM Response Parsing Error: ${message}`, context);
    this.rawResponse = rawResponse;
  }
}

/**
 * API server errors (5xx responses)
 */
export class VllmServerError extends VllmError {
  readonly code = 'VLLM_SERVER_ERROR';
  readonly statusCode: number;
  readonly httpStatus: number;

  constructor(httpStatus: number, message: string, context?: Record<string, any>) {
    super(`vLLM Server Error (${httpStatus}): ${message}`, context);
    this.httpStatus = httpStatus;
    this.statusCode = httpStatus;
  }
}

/**
 * API client errors (4xx responses)
 */
export class VllmClientError extends VllmError {
  readonly code = 'VLLM_CLIENT_ERROR';
  readonly statusCode: number;
  readonly httpStatus: number;

  constructor(httpStatus: number, message: string, context?: Record<string, any>) {
    super(`vLLM Client Error (${httpStatus}): ${message}`, context);
    this.httpStatus = httpStatus;
    this.statusCode = httpStatus;
  }
}

/**
 * Model-specific errors
 */
export class VllmModelError extends VllmError {
  readonly code = 'VLLM_MODEL_ERROR';
  readonly statusCode = 400;
  readonly modelName: string;

  constructor(modelName: string, message: string, context?: Record<string, any>) {
    super(`vLLM Model Error (${modelName}): ${message}`, context);
    this.modelName = modelName;
  }
}

/**
 * Chat format conversion errors
 */
export class VllmChatFormatError extends VllmError {
  readonly code = 'VLLM_CHAT_FORMAT_ERROR';
  readonly statusCode = 400;

  constructor(message: string, context?: Record<string, any>) {
    super(`vLLM Chat Format Error: ${message}`, context);
  }
}

/**
 * Tool calling errors
 */
export class VllmToolCallingError extends VllmError {
  readonly code = 'VLLM_TOOL_CALLING_ERROR';
  readonly statusCode = 400;

  constructor(message: string, context?: Record<string, any>) {
    super(`vLLM Tool Calling Error: ${message}`, context);
  }
}

/**
 * Error factory for creating appropriate error types based on HTTP responses
 */
export class VllmErrorFactory {
  static createFromHttpResponse(
    status: number,
    statusText: string,
    responseBody?: string,
    context?: Record<string, any>
  ): VllmError {
    const message = responseBody || statusText;

    if (status === 401) {
      return new VllmAuthenticationError(message, context);
    }

    if (status === 408) {
      return new VllmTimeoutError(30000, context); // Default timeout
    }

    if (status === 429) {
      // Try to extract retry-after from response
      let retryAfter: number | undefined;
      try {
        const parsed = JSON.parse(responseBody || '{}');
        retryAfter = parsed.retry_after || parsed.retryAfter;
      } catch {
        // Ignore parsing errors
      }
      return new VllmRateLimitError(message, retryAfter, context);
    }

    if (status >= 400 && status < 500) {
      return new VllmClientError(status, message, context);
    }

    if (status >= 500) {
      return new VllmServerError(status, message, context);
    }

    // Fallback for unexpected status codes
    return new VllmServerError(status, message, context);
  }

  static createFromNetworkError(error: Error, context?: Record<string, any>): VllmError {
    if (error.name === 'AbortError') {
      return new VllmTimeoutError(30000, context);
    }

    if (error.message.includes('fetch') || error.message.includes('network')) {
      return new VllmNetworkError(error.message, context);
    }

    return new VllmNetworkError(`Network error: ${error.message}`, context);
  }

  static createFromParsingError(error: Error, rawResponse?: string, context?: Record<string, any>): VllmError {
    return new VllmResponseParsingError(error.message, rawResponse, context);
  }
}

/**
 * Error handler utility for consistent error processing
 */
export class VllmErrorHandler {
  /**
   * Determine if an error is retryable
   */
  static isRetryable(error: VllmError): boolean {
    return (
      error instanceof VllmNetworkError ||
      error instanceof VllmTimeoutError ||
      error instanceof VllmRateLimitError ||
      (error instanceof VllmServerError && error.httpStatus >= 500)
    );
  }

  /**
   * Get retry delay for retryable errors
   */
  static getRetryDelay(error: VllmError, attempt: number): number {
    if (error instanceof VllmRateLimitError && error.retryAfter) {
      return error.retryAfter * 1000; // Convert to milliseconds
    }

    // Exponential backoff: 1s, 2s, 4s, 8s, etc.
    return Math.min(1000 * Math.pow(2, attempt - 1), 30000);
  }

  /**
   * Check if error should trigger a fallback response
   */
  static shouldFallback(error: VllmError): boolean {
    return (
      error instanceof VllmNetworkError ||
      error instanceof VllmTimeoutError ||
      error instanceof VllmServerError ||
      error instanceof VllmConfigurationError
    );
  }

  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(error: VllmError): string {
    if (error instanceof VllmNetworkError || error instanceof VllmTimeoutError) {
      return 'I\'m having trouble connecting to the AI service. Please try again in a moment.';
    }

    if (error instanceof VllmRateLimitError) {
      return 'The AI service is currently busy. Please wait a moment before trying again.';
    }

    if (error instanceof VllmAuthenticationError) {
      return 'There\'s an authentication issue with the AI service. Please contact support.';
    }

    if (error instanceof VllmServerError) {
      return 'The AI service is temporarily unavailable. Please try again later.';
    }

    return 'I encountered an error while processing your request. Please try again.';
  }
}
