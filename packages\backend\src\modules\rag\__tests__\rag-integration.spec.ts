/**
 * RAG HTTP Client Integration Tests
 *
 * Tests for the backend's HTTP communication with the RAG system microservice.
 * These tests focus on the HTTP client functionality, not the RAG system internals.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { RagService } from '../rag.service';
import { RagController } from '../rag.controller';
import { RagHttpClientService } from '../rag-http-client.service';
import { getTestConfig, shouldSkipExternalServices } from '../../../test/test-config.util';

describe('RAG HTTP Client Integration Tests', () => {
  let ragService: RagService;
  let ragController: RagController;
  let testConfig: ReturnType<typeof getTestConfig>;

  beforeAll(() => {
    if (shouldSkipExternalServices()) {
      console.log('Skipping RAG integration tests - external services disabled');
      return;
    }
    
    try {
      testConfig = getTestConfig();
    } catch (error) {
      console.log('Skipping RAG integration tests - configuration missing:', error);
      return;
    }
  });

  beforeEach(async () => {
    if (shouldSkipExternalServices() || !testConfig) {
      return;
    }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RagHttpClientService,
        RagService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                'RAG_ENABLED': 'true',
                'RAG_SYSTEM_URL': testConfig.ragSystem.url,
                'RAG_AUTO_INIT': 'false', // Don't auto-initialize in tests
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
      controllers: [RagController],
    }).compile();

    ragService = module.get<RagService>(RagService);
    ragController = module.get<RagController>(RagController);
  });

  describe('RAG Service Business Logic', () => {
    it('should handle service initialization', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      // Test that the service can be initialized without throwing
      expect(ragService).toBeDefined();
      expect(typeof ragService.initializeWithFaqData).toBe('function');
    });

    it('should have required service methods', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      // Test that all required methods exist
      expect(typeof ragService.searchRelevantFaqs).toBe('function');
      expect(typeof ragService.addFaqEntries).toBe('function');
      expect(typeof ragService.updateFaqEntries).toBe('function');
      expect(typeof ragService.deleteFaqEntries).toBe('function');
      expect(typeof ragService.getCategories).toBe('function');
      expect(typeof ragService.getStatistics).toBe('function');
      expect(typeof ragService.isAvailable).toBe('function');
    });
  });

  describe('HTTP Communication Tests', () => {
    it('should handle RAG system communication errors gracefully', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      // Test that the service handles communication errors properly
      // This tests the backend's error handling, not the RAG system functionality
      const isAvailable = await ragService.isAvailable();
      expect(typeof isAvailable).toBe('boolean');
    }, 10000);

  });

  describe('Controller Business Logic', () => {
    it('should have required controller methods', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      // Test that controller has required methods
      expect(ragController).toBeDefined();
      expect(typeof ragController.searchFaqs).toBe('function');
      expect(typeof ragController.getStatistics).toBe('function');
      expect(typeof ragController.getCategories).toBe('function');
    });

  });
});
