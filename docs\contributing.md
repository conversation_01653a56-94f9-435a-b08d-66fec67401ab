# Contributing Guide

Thank you for your interest in contributing to the OTRS AI-Powered Chat Assistant! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

Please read and follow our [Code of Conduct](./CODE_OF_CONDUCT.md) to foster an inclusive and respectful community.

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Yarn (v1.22 or later)
- Python 3.8+ (for model tuning)
- Git

### Setting Up the Development Environment

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/your-username/otrs-ai-powered.git
   cd otrs-ai-powered
   ```
3. Add the original repository as a remote:
   ```bash
   git remote add upstream https://github.com/original-owner/otrs-ai-powered.git
   ```
4. Install dependencies:
   ```bash
   yarn install
   ```
5. Run the setup script:
   ```bash
   bash scripts/setup-dev.sh
   ```
6. Start the development servers:
   ```bash
   yarn dev
   ```

## Development Workflow

### Branching Strategy

We use a simplified Git flow with the following branches:

- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Feature branches
- `bugfix/*`: Bug fix branches
- `hotfix/*`: Hot fix branches for production issues

### Creating a New Feature

1. Create a new branch from `develop`:
   ```bash
   git checkout develop
   git pull upstream develop
   git checkout -b feature/your-feature-name
   ```
2. Implement your changes
3. Commit your changes with meaningful commit messages:
   ```bash
   git commit -m "feat: add new feature"
   ```
4. Push your branch to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```
5. Create a pull request to the `develop` branch of the original repository

### Commit Message Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Example:
```
feat(frontend): add chat widget collapse animation
```

### Pull Request Process

1. Ensure your code follows the project's coding standards
2. Update documentation if necessary
3. Add tests for new features
4. Ensure all tests pass
5. Request a review from maintainers
6. Address review feedback

## Coding Standards

### General Guidelines

- Write clean, readable, and maintainable code
- Follow the DRY (Don't Repeat Yourself) principle
- Keep functions and methods small and focused
- Use meaningful variable and function names
- Add comments for complex logic

### TypeScript Guidelines

- Use TypeScript's type system effectively
- Define interfaces for data structures
- Use enums for fixed sets of values
- Avoid using `any` type when possible
- Use async/await for asynchronous code

### React Guidelines

- Use functional components with hooks
- Keep components small and focused
- Use React Context for state management
- Follow the React component lifecycle
- Use proper prop types

### NestJS Guidelines

- Follow the module-based architecture
- Use dependency injection
- Use DTOs for data validation
- Implement proper error handling
- Use guards for authentication and authorization

## Testing

### Writing Tests

- Write unit tests for individual functions and components
- Write integration tests for API endpoints
- Write end-to-end tests for critical user flows

### Running Tests

```bash
# Run all tests
yarn test

# Run tests for a specific package
yarn workspace @otrs-ai-powered/frontend test
```

## Documentation

### Code Documentation

- Add JSDoc comments for functions and classes
- Document complex algorithms and business logic
- Keep comments up-to-date with code changes

### Project Documentation

- Update README files when adding new features
- Document API changes in the API reference
- Add usage examples for new features

## Submitting Issues

### Bug Reports

When submitting a bug report, please include:

- A clear and descriptive title
- Steps to reproduce the issue
- Expected behavior
- Actual behavior
- Screenshots or error messages
- Environment information (OS, browser, etc.)

### Feature Requests

When submitting a feature request, please include:

- A clear and descriptive title
- A detailed description of the proposed feature
- Use cases and benefits
- Any relevant examples or mockups

## Review Process

1. A maintainer will review your pull request
2. They may request changes or ask questions
3. Once approved, your pull request will be merged
4. Your contribution will be acknowledged in the release notes

## Release Process

1. Features are merged into the `develop` branch
2. When ready for release, `develop` is merged into `main`
3. A new version tag is created
4. Release notes are generated
5. The new version is deployed

## Getting Help

If you need help with contributing, you can:

- Join our community chat
- Ask questions in GitHub issues
- Contact the maintainers directly

Thank you for contributing to the OTRS AI-Powered Chat Assistant!
