/**
 * Error codes for the application
 */
export enum ErrorCode {
  // Authentication errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  
  // Chat errors
  MESSAGE_FAILED = 'MESSAGE_FAILED',
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  
  // OTRS errors
  TICKET_CREATION_FAILED = 'TICKET_CREATION_FAILED',
  TICKET_UPDATE_FAILED = 'TICKET_UPDATE_FAILED',
  TICKET_NOT_FOUND = 'TICKET_NOT_FOUND',
  
  // LLM errors
  LLM_REQUEST_FAILED = 'LLM_REQUEST_FAILED',
  TOOL_EXECUTION_FAILED = 'TOOL_EXECUTION_FAILED',
  
  // RAG errors
  EMBEDDING_FAILED = 'EMBEDDING_FAILED',
  RETRIEVAL_FAILED = 'RETRIEVAL_FAILED',
  
  // General errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  BAD_REQUEST = 'BAD_REQUEST',
}

/**
 * Error messages for the application
 */
export const ErrorMessage = {
  [ErrorCode.INVALID_CREDENTIALS]: 'Invalid username or password',
  [ErrorCode.TOKEN_EXPIRED]: 'Authentication token has expired',
  [ErrorCode.UNAUTHORIZED]: 'You are not authenticated',
  [ErrorCode.FORBIDDEN]: 'You do not have permission to access this resource',
  
  [ErrorCode.MESSAGE_FAILED]: 'Failed to send message',
  [ErrorCode.SESSION_NOT_FOUND]: 'Chat session not found',
  
  [ErrorCode.TICKET_CREATION_FAILED]: 'Failed to create OTRS ticket',
  [ErrorCode.TICKET_UPDATE_FAILED]: 'Failed to update OTRS ticket',
  [ErrorCode.TICKET_NOT_FOUND]: 'OTRS ticket not found',
  
  [ErrorCode.LLM_REQUEST_FAILED]: 'Failed to communicate with LLM service',
  [ErrorCode.TOOL_EXECUTION_FAILED]: 'Failed to execute tool',
  
  [ErrorCode.EMBEDDING_FAILED]: 'Failed to generate embeddings',
  [ErrorCode.RETRIEVAL_FAILED]: 'Failed to retrieve relevant information',
  
  [ErrorCode.VALIDATION_ERROR]: 'Validation error',
  [ErrorCode.INTERNAL_SERVER_ERROR]: 'Internal server error',
  [ErrorCode.NOT_FOUND]: 'Resource not found',
  [ErrorCode.BAD_REQUEST]: 'Bad request',
};
