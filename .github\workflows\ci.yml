name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  # lint:
  #   name: Lint
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v3

  #     - name: Set up Node.js
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: "18"
  #         cache: "yarn"

  #     - name: Install dependencies
  #       run: yarn install --frozen-lockfile

  #     - name: Lint
  #       run: yarn lint

  # test:
  #   name: Test
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v3

  #     - name: Set up Node.js
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: "18"
  #         cache: "yarn"

  #     - name: Install dependencies
  #       run: yarn install --frozen-lockfile

  #     - name: Build shared package
  #       run: yarn workspace @otrs-ai-powered/shared build

  #     - name: Run tests
  #       run: yarn test

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build
        run: yarn build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            packages/*/dist
            packages/frontend/build
          retention-days: 7
