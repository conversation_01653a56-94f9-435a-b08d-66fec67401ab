import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>t<PERSON>ontroller } from './chat.controller';
import { ChatService } from './chat.service';
import { ChatGateway } from './chat.gateway';
import { LlmModule } from '../llm/llm.module';
import { ToolCallingModule } from '../tool-calling/tool-calling.module';
import { AuthModule } from '../auth/auth.module';
import { RagModule } from '../rag/rag.module';
import { RagEnhancedLlmService } from '../llm/rag-enhanced-llm.service';

@Module({
  imports: [LlmModule, ToolCallingModule, AuthModule, RagModule],
  controllers: [ChatController],
  providers: [ChatService, ChatGateway, RagEnhancedLlmService],
  exports: [ChatService],
})
export class ChatModule {}
