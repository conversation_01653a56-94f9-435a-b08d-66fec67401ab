#!/bin/bash

# Exit on error
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up development environment for OTRS AI-Powered Chat Assistant...${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js is not installed. Please install Node.js v18 or later.${NC}"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo -e "${RED}Node.js version must be 18 or higher. Current version: $(node -v)${NC}"
    exit 1
fi

# Check if Yarn is installed
if ! command -v yarn &> /dev/null; then
    echo -e "${YELLOW}Yarn is not installed. Installing Yarn...${NC}"
    npm install -g yarn
fi

# Create .env.local file if it doesn't exist
if [ ! -f .env.local ]; then
    echo -e "${YELLOW}Creating .env.local file from .env.example...${NC}"
    cp .env.example .env.local
    echo -e "${YELLOW}Please update .env.local with your configuration values.${NC}"
fi

# Install dependencies
echo -e "${GREEN}Installing dependencies...${NC}"
yarn install

# Build shared package
echo -e "${GREEN}Building shared package...${NC}"
yarn workspace @otrs-ai-powered/shared build

# Create data directories for model tuning
echo -e "${GREEN}Creating data directories for model tuning...${NC}"
mkdir -p packages/model-tuning/data/raw
mkdir -p packages/model-tuning/data/processed
mkdir -p packages/model-tuning/models
mkdir -p packages/model-tuning/logs
mkdir -p packages/model-tuning/evaluations

# Create documentation directory
echo -e "${GREEN}Creating documentation directory...${NC}"
mkdir -p docs

# Setup complete
echo -e "${GREEN}Development environment setup complete!${NC}"
echo -e "${GREEN}You can now start the development servers with:${NC}"
echo -e "${YELLOW}yarn dev${NC}"
echo -e "${GREEN}Or start individual services with:${NC}"
echo -e "${YELLOW}yarn workspace @otrs-ai-powered/frontend dev${NC}"
echo -e "${YELLOW}yarn workspace @otrs-ai-powered/backend dev${NC}"
echo -e "${YELLOW}yarn workspace @otrs-ai-powered/mcp-server dev${NC}"
echo -e "${YELLOW}yarn workspace @otrs-ai-powered/rag-system dev${NC}"
