/**
 * Represents a document in the RAG system
 */
export interface Document {
  id: string;
  content: string;
  metadata: DocumentMetadata;
}

/**
 * Metadata for a document
 */
export interface DocumentMetadata {
  source: string;
  title?: string;
  author?: string;
  createdAt?: string;
  updatedAt?: string;
  url?: string;
  tags?: string[];
  category?: string;
  [key: string]: any;
}

/**
 * Represents a document with its embedding
 */
export interface DocumentWithEmbedding extends Document {
  embedding: number[];
}

/**
 * FAQ entry structure from Excel data
 */
export interface FaqEntry {
  id: string;
  category: string;
  question: string;
  answer: string;
  metadata?: {
    source: string;
    lastUpdated: string;
    tags?: string[];
    priority?: 'low' | 'medium' | 'high';
    language?: string;
  };
}

/**
 * Processed document ready for vectorization
 */
export interface ProcessedDocument {
  id: string;
  content: string;
  embedding?: number[];
  metadata: {
    type: 'faq' | 'document' | 'article';
    category: string;
    title: string;
    source: string;
    lastUpdated: string;
    contentLength: number;
    tags?: string[];
  };
}

/**
 * Vector document for storage in Pinecone
 */
export interface VectorDocument {
  id: string;
  vector: number[];
  metadata: {
    content: string;
    category: string;
    question: string;
    answer: string;
    source: string;
    lastUpdated: string;
    contentLength: number;
  };
}

/**
 * Result of data processing pipeline
 */
export interface DataProcessingResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: Array<{
    index: number;
    error: string;
    data?: any;
  }>;
  statistics: {
    averageQuestionLength: number;
    averageAnswerLength: number;
    categoriesCount: number;
    categories: string[];
  };
}
