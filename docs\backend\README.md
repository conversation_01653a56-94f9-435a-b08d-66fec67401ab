# Backend Documentation

This directory contains documentation for the backend component of the OTRS AI-Powered Chat Assistant.

## Overview

The backend is built with NestJS, a progressive Node.js framework for building efficient and scalable server-side applications. It provides RESTful API endpoints for chat communication and integrates with the LLM service and MCP Server.

## Key Features

- RESTful API endpoints
- WebSocket support for real-time communication
- Authentication and authorization
- Integration with LLM services
- Integration with MCP Server for tool execution
- Integration with RAG System for knowledge retrieval

## Directory Structure

```
packages/backend/
├── src/
│   ├── main.ts                    # Application entry point
│   ├── app.module.ts              # Root application module
│   ├── config/                    # Configuration
│   │   ├── app.config.ts          # Application configuration
│   │   ├── database.config.ts     # Database configuration
│   │   └── llm.config.ts          # LLM service configuration
│   ├── modules/
│   │   ├── auth/                  # Authentication module
│   │   │   ├── auth.module.ts
│   │   │   ├── auth.controller.ts
│   │   │   ├── auth.service.ts
│   │   │   ├── strategies/        # Passport strategies
│   │   │   └── guards/            # Authentication guards
│   │   ├── chat/                  # Chat module
│   │   │   ├── chat.module.ts
│   │   │   ├── chat.controller.ts
│   │   │   ├── chat.service.ts
│   │   │   ├── chat.gateway.ts    # WebSocket gateway
│   │   │   └── dto/               # Data Transfer Objects
│   │   ├── llm/                   # LLM integration module
│   │   │   ├── llm.module.ts
│   │   │   ├── llm.service.ts
│   │   │   └── providers/         # Different LLM providers
│   │   └── mcp-client/            # MCP client module
│   │       ├── mcp-client.module.ts
│   │       ├── mcp-client.service.ts
│   │       └── interfaces/        # MCP interfaces
│   ├── common/                    # Common utilities and middleware
│   │   ├── filters/               # Exception filters
│   │   ├── interceptors/          # HTTP interceptors
│   │   ├── decorators/            # Custom decorators
│   │   └── pipes/                 # Validation pipes
│   └── types/                     # TypeScript type definitions
└── ...
```

## Getting Started

### Development

```bash
# Start the development server
yarn workspace @otrs-ai-powered/backend dev
```

### Building

```bash
# Build for production
yarn workspace @otrs-ai-powered/backend build
```

### Testing

```bash
# Run tests
yarn workspace @otrs-ai-powered/backend test
```

## API Documentation

The backend provides the following API endpoints:

### Authentication

- `POST /api/auth/login`: Authenticate a user
- `POST /api/auth/logout`: Log out a user
- `POST /api/auth/refresh-token`: Refresh an access token
- `GET /api/auth/me`: Get the current user's information

### Chat

- `POST /api/chat/sessions`: Create a new chat session
- `GET /api/chat/sessions/:sessionId`: Get a specific chat session
- `GET /api/chat/history`: Get chat history for the current user
- `DELETE /api/chat/sessions/:sessionId`: Delete a chat session
- `POST /api/chat/messages`: Send a message in a chat session

## WebSocket Events

The backend provides the following WebSocket events:

- `connect`: Client connected to the WebSocket server
- `disconnect`: Client disconnected from the WebSocket server
- `message`: New message received
- `typing`: User is typing

## Authentication and Authorization

The backend uses JWT (JSON Web Tokens) for authentication. The authentication flow is as follows:

1. User logs in with username and password
2. Backend validates credentials and issues access and refresh tokens
3. Client includes the access token in the Authorization header for subsequent requests
4. When the access token expires, the client can use the refresh token to get a new access token

## Error Handling

The backend uses NestJS exception filters to handle errors. The error responses follow this format:

```json
{
  "statusCode": 400,
  "message": "Error message",
  "error": "Error type"
}
```
