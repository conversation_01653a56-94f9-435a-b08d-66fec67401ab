import OpenAI from 'openai';
import ModelClient, { isUnexpected } from '@azure-rest/ai-inference';
import { AzureKeyCredential } from '@azure/core-auth';
import { logger } from '../utils/logger';

interface VllmEmbeddingResponse {
  object: string;
  data: Array<{
    object: string;
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
    completion_tokens: number;
  };
}

export interface EmbeddingConfig {
  provider: 'openai' | 'vllm' | 'azure';
  apiKey: string;
  baseUrl?: string; // For vLLM and Azure providers
  model: string;
  dimensions?: number;
  batchSize?: number;
  timeout?: number;
}

export interface EmbeddingResult {
  embedding: number[];
  tokenCount: number;
  model: string;
}

export interface BatchEmbeddingResult {
  embeddings: number[][];
  totalTokens: number;
  model: string;
  processedCount: number;
  failedCount: number;
  errors: Array<{
    index: number;
    text: string;
    error: string;
  }>;
}

/**
 * Service for generating embeddings from text using various providers
 */
export class EmbeddingService {
  private readonly config: EmbeddingConfig;
  private readonly client: OpenAI | null;
  private readonly azureClient: ReturnType<typeof ModelClient> | null;

  constructor(config: EmbeddingConfig) {
    this.config = config;

    // Initialize OpenAI client only for OpenAI provider
    if (config.provider === 'openai') {
      this.client = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseUrl,
        timeout: config.timeout || 30000,
      });
      this.azureClient = null;
    } else if (config.provider === 'azure') {
      // Initialize Azure AI client for Azure provider
      if (!config.baseUrl) {
        throw new Error('Base URL is required for Azure AI provider');
      }

      // Construct the correct Azure AI endpoint URL with deployment path
      // Format: https://{resource}.cognitiveservices.azure.com/openai/deployments/{deployment-name}
      const deploymentUrl = `${config.baseUrl}/openai/deployments/${config.model}`;

      logger.info(`Initializing Azure AI client with deployment URL: ${deploymentUrl}`);

      this.client = null;
      this.azureClient = new (ModelClient as any)(
        deploymentUrl,
        new AzureKeyCredential(config.apiKey)
      );
    } else {
      // For vLLM, use direct HTTP calls
      this.client = null;
      this.azureClient = null;
    }
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    try {
      logger.debug(`Generating embedding for text (${text.length} chars) using ${this.config.provider}`);

      // Clean and prepare text
      const cleanText = this.preprocessText(text);

      if (this.config.provider === 'openai' && this.client) {
        return await this.generateOpenAIEmbedding(cleanText);
      } else if (this.config.provider === 'vllm') {
        return await this.generateVllmEmbedding(cleanText);
      } else if (this.config.provider === 'azure') {
        return await this.generateAzureEmbedding(cleanText);
      } else {
        throw new Error(`Unsupported provider: ${this.config.provider}`);
      }

    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate embedding using OpenAI client
   */
  private async generateOpenAIEmbedding(text: string): Promise<EmbeddingResult> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized');
    }

    const response = await this.client.embeddings.create({
      model: this.config.model,
      input: text,
      dimensions: this.config.dimensions,
    });

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data received from OpenAI API');
    }

    const embedding = response.data[0].embedding;
    const tokenCount = response.usage?.total_tokens || 0;

    logger.debug(`Generated OpenAI embedding with ${embedding.length} dimensions, ${tokenCount} tokens`);

    return {
      embedding,
      tokenCount,
      model: this.config.model,
    };
  }

  /**
   * Generate embedding using vLLM server with direct HTTP calls
   */
  private async generateVllmEmbedding(text: string): Promise<EmbeddingResult> {
    if (!this.config.baseUrl) {
      throw new Error('Base URL is required for vLLM provider');
    }

    const response = await fetch(`${this.config.baseUrl}/v1/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model,
        input: text,
        encoding_format: 'float',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`vLLM API error (${response.status}): ${errorText}`);
    }

    const data: VllmEmbeddingResponse = await response.json();

    if (!data.data || data.data.length === 0) {
      throw new Error('No embedding data received from vLLM API');
    }

    const embedding = data.data[0].embedding;
    const tokenCount = data.usage?.total_tokens || 0;

    logger.debug(`Generated vLLM embedding with ${embedding.length} dimensions, ${tokenCount} tokens`);

    return {
      embedding,
      tokenCount,
      model: this.config.model,
    };
  }

  /**
   * Generate embedding using Azure AI SDK
   */
  private async generateAzureEmbedding(text: string): Promise<EmbeddingResult> {
    if (!this.azureClient) {
      throw new Error('Azure AI client is not initialized');
    }

    const requestBody = {
      input: [text],
      model: this.config.model,
      dimensions: this.config.dimensions || 384,
      encoding_format: 'float',
    };

    logger.debug('Azure AI embedding request:', {
      endpoint: '/embeddings',
      model: this.config.model,
      inputLength: text.length,
      dimensions: this.config.dimensions || 384,
      requestBody: JSON.stringify(requestBody, null, 2)
    });

    try {
      const response = await this.azureClient.path("/embeddings").post({
        body: requestBody
      });

      logger.debug('Azure AI raw response:', {
        status: response.status,
        headers: response.headers,
        bodyType: typeof response.body
      });

      // Use Azure AI SDK's error checking helper
      if (isUnexpected(response)) {
        logger.error('Azure AI unexpected response:', {
          status: response.status,
          error: response.body
        });
        throw response.body.error;
      }

      const data = response.body;

      logger.debug('Azure AI response data:', {
        hasData: !!data.data,
        dataLength: data.data?.length,
        hasUsage: !!data.usage,
        fullResponse: JSON.stringify(data, null, 2)
      });

      if (!data.data || data.data.length === 0) {
        throw new Error('No embedding data received from Azure AI API');
      }

      const embedding = data.data[0].embedding as number[];
      const tokenCount = data.usage?.total_tokens || 0;

      logger.info(`✅ Generated Azure AI embedding: ${embedding.length} dimensions, ${tokenCount} tokens`);

      return {
        embedding,
        tokenCount,
        model: this.config.model,
      };
    } catch (error) {
      logger.error('Error generating Azure AI embedding:', error);
      throw new Error(`Failed to generate Azure AI embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embeddings for multiple texts in batches
   */
  async generateBatchEmbeddings(texts: string[]): Promise<BatchEmbeddingResult> {
    try {
      logger.info(`Generating embeddings for ${texts.length} texts`);

      const batchSize = this.config.batchSize || 100;
      const embeddings: number[][] = [];
      let totalTokens = 0;
      let processedCount = 0;
      let failedCount = 0;
      const errors: BatchEmbeddingResult['errors'] = [];

      // Process in batches
      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(texts.length / batchSize)}`);

        try {
          const batchResult = await this.processBatch(batch, i);
          embeddings.push(...batchResult.embeddings);
          totalTokens += batchResult.totalTokens;
          processedCount += batchResult.processedCount;
          failedCount += batchResult.failedCount;
          errors.push(...batchResult.errors);

        } catch (error) {
          logger.error(`Error processing batch ${Math.floor(i / batchSize) + 1}:`, error);

          // Mark entire batch as failed
          for (let j = 0; j < batch.length; j++) {
            errors.push({
              index: i + j,
              text: batch[j].substring(0, 100) + '...',
              error: error instanceof Error ? error.message : String(error),
            });
            failedCount++;
          }
        }

        // Add delay between batches to avoid rate limiting
        if (i + batchSize < texts.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.info(`Batch embedding complete: ${processedCount} successful, ${failedCount} failed`);

      return {
        embeddings,
        totalTokens,
        model: this.config.model,
        processedCount,
        failedCount,
        errors,
      };

    } catch (error) {
      logger.error('Error in batch embedding generation:', error);
      throw error;
    }
  }

  /**
   * Process a single batch of texts
   */
  private async processBatch(texts: string[], _startIndex: number): Promise<BatchEmbeddingResult> {
    const cleanTexts = texts.map(text => this.preprocessText(text));

    if (this.config.provider === 'openai' && this.client) {
      return await this.processBatchOpenAI(cleanTexts);
    } else if (this.config.provider === 'vllm') {
      return await this.processBatchVllm(cleanTexts);
    } else if (this.config.provider === 'azure') {
      return await this.processBatchAzure(cleanTexts);
    } else {
      throw new Error(`Unsupported provider for batch processing: ${this.config.provider}`);
    }
  }

  /**
   * Process batch using OpenAI client
   */
  private async processBatchOpenAI(texts: string[]): Promise<BatchEmbeddingResult> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized');
    }

    const response = await this.client.embeddings.create({
      model: this.config.model,
      input: texts,
      dimensions: this.config.dimensions,
    });

    if (!response.data || response.data.length === 0) {
      throw new Error('No embedding data received from OpenAI API');
    }

    const embeddings = response.data.map(item => item.embedding);
    const totalTokens = response.usage?.total_tokens || 0;

    return {
      embeddings,
      totalTokens,
      model: this.config.model,
      processedCount: embeddings.length,
      failedCount: 0,
      errors: [],
    };
  }

  /**
   * Process batch using vLLM server
   */
  private async processBatchVllm(texts: string[]): Promise<BatchEmbeddingResult> {
    if (!this.config.baseUrl) {
      throw new Error('Base URL is required for vLLM provider');
    }

    const response = await fetch(`${this.config.baseUrl}/v1/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model,
        input: texts,
        encoding_format: 'float',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`vLLM API error (${response.status}): ${errorText}`);
    }

    const data: VllmEmbeddingResponse = await response.json();

    if (!data.data || data.data.length === 0) {
      throw new Error('No embedding data received from vLLM API');
    }

    const embeddings = data.data.map(item => item.embedding);
    const totalTokens = data.usage?.total_tokens || 0;

    return {
      embeddings,
      totalTokens,
      model: this.config.model,
      processedCount: embeddings.length,
      failedCount: 0,
      errors: [],
    };
  }

  /**
   * Process batch using Azure AI SDK
   */
  private async processBatchAzure(texts: string[]): Promise<BatchEmbeddingResult> {
    if (!this.azureClient) {
      throw new Error('Azure AI client is not initialized');
    }

    const requestBody = {
      input: texts,
      model: this.config.model,
      dimensions: this.config.dimensions || 384,
      encoding_format: 'float',
    };

    logger.debug('Azure AI batch embedding request:', {
      endpoint: '/embeddings',
      model: this.config.model,
      inputCount: texts.length,
      dimensions: this.config.dimensions || 384,
      requestBody: JSON.stringify(requestBody, null, 2)
    });

    try {
      const response = await this.azureClient.path("/embeddings").post({
        body: requestBody
      });

      logger.debug('Azure AI batch raw response:', {
        status: response.status,
        headers: response.headers,
        bodyType: typeof response.body
      });

      // Use Azure AI SDK's error checking helper
      if (isUnexpected(response)) {
        logger.error('Azure AI batch unexpected response:', {
          status: response.status,
          error: response.body
        });
        throw response.body.error;
      }

      const data = response.body;

      logger.debug('Azure AI batch response data:', {
        hasData: !!data.data,
        dataLength: data.data?.length,
        hasUsage: !!data.usage,
        fullResponse: JSON.stringify(data, null, 2)
      });

      if (!data.data || data.data.length === 0) {
        throw new Error('No embedding data received from Azure AI API');
      }

      const embeddings = data.data.map((item: any) => item.embedding as number[]);
      const totalTokens = data.usage?.total_tokens || 0;

      logger.info(`✅ Generated ${embeddings.length} Azure AI batch embeddings, ${totalTokens} tokens`);

      return {
        embeddings,
        totalTokens,
        model: this.config.model,
        processedCount: embeddings.length,
        failedCount: 0,
        errors: [],
      };
    } catch (error) {
      logger.error('Error processing batch with Azure AI:', error);
      throw new Error(`Failed to process batch with Azure AI: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Preprocess text before embedding generation
   */
  private preprocessText(text: string): string {
    // Validate input
    if (typeof text !== 'string') {
      throw new Error('Input must be a string');
    }

    // Handle empty or whitespace-only text
    const trimmedText = text.trim();
    if (trimmedText.length === 0) {
      throw new Error('Input text cannot be empty or contain only whitespace');
    }

    // Clean and normalize text
    let processedText = trimmedText
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[\r\n]+/g, ' ') // Replace line breaks with spaces
      .replace(/[^\x20-\x7E\u00A0-\uFFFF]/g, '') // Remove non-printable characters
      .trim();

    // Apply token-based truncation for sentence-transformers model
    // Sentence-transformers typically has a 256 token limit
    // Rough estimate: 1 token ≈ 4 characters for English text
    const maxTokens = this.getMaxTokensForModel();
    const maxChars = maxTokens * 3; // Conservative estimate: 3 chars per token

    if (processedText.length > maxChars) {
      logger.warn(`Text truncated from ${processedText.length} to ${maxChars} characters to fit token limit`);
      processedText = processedText.substring(0, maxChars);

      // Try to break at word boundary
      const lastSpaceIndex = processedText.lastIndexOf(' ');
      if (lastSpaceIndex > maxChars * 0.8) { // Only break at word if we don't lose too much
        processedText = processedText.substring(0, lastSpaceIndex);
      }
    }

    return processedText;
  }

  /**
   * Get maximum tokens for the current model
   */
  private getMaxTokensForModel(): number {
    // Model-specific token limits
    if (this.config.model?.includes('sentence-transformers')) {
      return 256; // sentence-transformers models typically have 256 token limit
    } else if (this.config.model?.includes('text-embedding-3-small') || this.config.model?.includes('embed-mini-all-l6v2') || this.config.model?.includes('text-embedding-ada-002')) {
      return 8191; // OpenAI text-embedding-3-small, Azure AI embed-mini-all-l6v2, or text-embedding-ada-002
    } else if (this.config.model?.includes('text-embedding-3-large')) {
      return 8191; // OpenAI text-embedding-3-large
    }

    // Default conservative limit
    return 256;
  }

  /**
   * Get embedding dimensions for the current model
   */
  getDimensions(): number {
    // Common embedding dimensions by model
    const dimensionMap: Record<string, number> = {
      'text-embedding-3-small': 384,
      'text-embedding-3-large': 3072,
      'text-embedding-ada-002': 384,
      'embed-mini-all-l6v2': 384, // Azure AI deployment for all-MiniLM-L6-v2 (384 dimensions)
      'microsoft/Phi-4-mini-instruct': 3072, // Phi-4 produces 3072-dimensional embeddings
      'sentence-transformers/all-MiniLM-L6-v2': 384, // Sentence Transformers model
    };

    return this.config.dimensions || dimensionMap[this.config.model] || 384;
  }

  /**
   * Check if the embedding service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      // Test with a simple embedding request
      await this.generateEmbedding('test');
      return true;
    } catch (error) {
      logger.warn('Embedding service is not available:', error);
      return false;
    }
  }

  /**
   * Get service information
   */
  getServiceInfo(): { provider: string; model: string; dimensions: number } {
    return {
      provider: this.config.provider,
      model: this.config.model,
      dimensions: this.getDimensions(),
    };
  }
}
