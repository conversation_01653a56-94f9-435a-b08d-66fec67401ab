apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: your-org/otrs-ai-powered:latest-backend
        ports:
        - containerPort: 4000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "4000"
        - name: CORS_ORIGIN
          value: "https://chat.example.com"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: otrs-ai-secrets
              key: JWT_SECRET
        - name: LLM_PROVIDER
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: LLM_PROVIDER
        - name: LLM_API_KEY
          valueFrom:
            secretKeyRef:
              name: otrs-ai-secrets
              key: LLM_API_KEY
        - name: LLM_MODEL
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: LLM_MODEL
        - name: MCP_SERVER_URL
          value: "http://mcp-server:5000"
        - name: RAG_SYSTEM_URL
          value: "http://rag-system:6000"
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 4000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 4000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend
spec:
  selector:
    app: backend
  ports:
  - port: 4000
    targetPort: 4000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend
            port:
              number: 4000
