import { Injectable, Logger } from '@nestjs/common';
import { ChatMessage } from '@otrs-ai-powered/shared';
import { LlmService } from './llm.service';
import { RagService } from '../rag/rag.service';
import { RelevantFaqResult } from '../rag/interfaces/rag.interface';


export interface RagEnhancedResponse {
  response: string;
  usedRag: boolean;
  ragResults?: RelevantFaqResult[];
  ragContext?: string;
  fallbackReason?: string;
}

@Injectable()
export class RagEnhancedLlmService {
  private readonly logger = new Logger(RagEnhancedLlmService.name);

  constructor(
    private readonly llmService: LlmService,
    private readonly ragService: RagService,
  ) {}

  /**
   * Generate response with RAG enhancement
   */
  async generateResponseWithRag(
    messages: ChatMessage[],
    options: {
      useRag?: boolean;
      ragThreshold?: number;
      maxRagResults?: number;
      fallbackToLlm?: boolean;
    } = {}
  ): Promise<RagEnhancedResponse> {
    const {
      useRag = true,
      ragThreshold = 0.6,
      maxRagResults = 3,
      fallbackToLlm = true,
    } = options;

    try {
      // Get the latest user message for RAG search
      const userMessage = this.getLatestUserMessage(messages);
      
      if (!userMessage || !useRag) {
        return await this.generateDirectLlmResponse(messages, 'RAG disabled or no user message');
      }

      // Determine if we should use RAG for this query
      const shouldUseRag = await this.shouldUseRag(userMessage);
      
      if (!shouldUseRag) {
        return await this.generateDirectLlmResponse(messages, 'Query not suitable for RAG');
      }

      // Search for relevant FAQ entries
      const ragResults = await this.ragService.searchRelevantFaqs(userMessage, maxRagResults);
      
      // Check if we have good enough results
      const goodResults = ragResults.filter(result => result.similarityScore >= ragThreshold);
      
      if (goodResults.length === 0) {
        if (fallbackToLlm) {
          return await this.generateDirectLlmResponse(messages, 'No relevant FAQ entries found');
        } else {
          return {
            response: 'I couldn\'t find relevant information in our FAQ database. Please try rephrasing your question or contact support.',
            usedRag: false,
            fallbackReason: 'No relevant FAQ entries found',
          };
        }
      }

      // Generate RAG-enhanced response
      return await this.generateRagEnhancedResponse(messages, goodResults);

    } catch (error) {
      this.logger.error('Error in RAG-enhanced generation:', error);
      
      if (fallbackToLlm) {
        return await this.generateDirectLlmResponse(messages, `RAG error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } else {
        throw error;
      }
    }
  }

  /**
   * Generate direct LLM response without RAG
   */
  private async generateDirectLlmResponse(
    messages: ChatMessage[], 
    fallbackReason?: string
  ): Promise<RagEnhancedResponse> {
    try {
      const response = await this.llmService.generateResponse(messages);
      
      return {
        response: response.content,
        usedRag: false,
        fallbackReason,
      };

    } catch (error) {
      this.logger.error('Error in direct LLM response:', error);
      throw error;
    }
  }

  /**
   * Generate RAG-enhanced response
   */
  private async generateRagEnhancedResponse(
    messages: ChatMessage[],
    ragResults: RelevantFaqResult[]
  ): Promise<RagEnhancedResponse> {
    try {
      // Create RAG context from FAQ results
      const ragContext = this.createRagContext(ragResults);

      // Create enhanced system message with RAG context
      const enhancedMessages = this.createRagEnhancedMessages(messages, ragContext);

      // Generate response with enhanced context
      const response = await this.llmService.generateResponse(enhancedMessages);

      // Validate and clean the response to ensure no FAQ metadata leaks
      const cleanedResponse = this.validateAndCleanResponse(response.content);

      return {
        response: cleanedResponse,
        usedRag: true,
        ragResults,
        ragContext,
      };

    } catch (error) {
      this.logger.error('Error generating RAG-enhanced response:', error);
      throw error;
    }
  }

  /**
   * Validate and clean response to ensure no FAQ metadata or structure leaks through
   */
  private validateAndCleanResponse(response: string): string {
    let cleanedResponse = response.trim();

    // Remove any FAQ-related metadata that might have leaked through
    const faqPatterns = [
      /FAQ \d+.*?:/gi,
      /Relevance:\s*\d+\.?\d*%/gi,
      /Category:\s*\w+/gi,
      /Q:\s*/gi,
      /A:\s*/gi,
      /RELEVANT FAQ INFORMATION:?/gi,
      /Based on the FAQ information/gi,
      /According to the FAQ/gi,
      /The FAQ states/gi,
      /FAQ entry/gi,
    ];

    // Remove FAQ patterns
    faqPatterns.forEach(pattern => {
      cleanedResponse = cleanedResponse.replace(pattern, '');
    });

    // Clean up any double spaces or line breaks caused by removals
    cleanedResponse = cleanedResponse
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();

    // If the response is empty after cleaning, provide a fallback
    if (!cleanedResponse || cleanedResponse.length < 10) {
      this.logger.warn('Response was too short after cleaning, using fallback');
      return 'I can help you with that. Could you please provide more specific details about what you need assistance with?';
    }

    return cleanedResponse;
  }

  /**
   * Create RAG context from FAQ results
   */
  private createRagContext(ragResults: RelevantFaqResult[]): string {
    // Extract and synthesize the knowledge from FAQ results without exposing structure
    const knowledgePoints = ragResults.map(result => {
      const { question, answer, category } = result.faqEntry;

      // Create natural knowledge statements without FAQ structure
      let knowledge = answer;

      // Add context about the topic if it helps clarify
      if (category && category !== 'General') {
        knowledge = `Regarding ${category.toLowerCase()}: ${answer}`;
      }

      // If the question provides additional context, incorporate it naturally
      if (question.toLowerCase() !== answer.toLowerCase()) {
        const questionContext = this.extractQuestionContext(question);
        if (questionContext) {
          knowledge = `${questionContext} ${knowledge}`;
        }
      }

      return knowledge;
    });

    // Combine knowledge points into natural context
    const combinedKnowledge = knowledgePoints.join(' Additionally, ');

    return `Based on your knowledge of OTRS and technical support, you know that: ${combinedKnowledge}

Use this knowledge to provide a direct, helpful response to the user's question. Do not reference FAQs, knowledge bases, or any source materials. Simply answer the question naturally as if this information is part of your training.`;
  }

  /**
   * Extract contextual information from FAQ questions to enhance knowledge
   */
  private extractQuestionContext(question: string): string {
    const lowerQuestion = question.toLowerCase();

    // Extract common question patterns to provide context
    if (lowerQuestion.includes('how to') || lowerQuestion.includes('how do')) {
      return 'For this type of task,';
    }
    if (lowerQuestion.includes('what is') || lowerQuestion.includes('what are')) {
      return 'To explain this concept,';
    }
    if (lowerQuestion.includes('why') || lowerQuestion.includes('when')) {
      return 'In this situation,';
    }
    if (lowerQuestion.includes('where') || lowerQuestion.includes('which')) {
      return 'For location or selection,';
    }
    if (lowerQuestion.includes('troubleshoot') || lowerQuestion.includes('error') || lowerQuestion.includes('problem')) {
      return 'When troubleshooting,';
    }

    return '';
  }

  /**
   * Create enhanced messages with RAG context
   */
  private createRagEnhancedMessages(messages: ChatMessage[], ragContext: string): ChatMessage[] {
    const enhancedMessages: ChatMessage[] = [...messages];

    // Find existing system message or create one
    const systemMessageIndex = enhancedMessages.findIndex(msg => msg.role === 'system');

    const enhancedSystemContent = `You are a helpful OTRS technical support assistant. Your goal is to provide clear, direct, and helpful responses to user questions.

${ragContext}

IMPORTANT INSTRUCTIONS:
- Provide a single, coherent response that directly answers the user's question
- Never mention FAQs, knowledge bases, documentation, or any source materials
- Never display relevance scores, categories, or metadata
- Do not show multiple response options or reference materials
- Respond naturally as if the knowledge is part of your training
- If you don't have specific information, say so clearly and offer general guidance
- Keep responses concise but complete`;

    if (systemMessageIndex >= 0) {
      // Replace existing system message with enhanced version
      const existingSystemMessage = enhancedMessages[systemMessageIndex];
      enhancedMessages[systemMessageIndex] = {
        ...existingSystemMessage,
        content: enhancedSystemContent,
      };
    } else {
      // Add new system message with RAG context
      enhancedMessages.unshift({
        id: 'rag-system-' + Date.now(),
        role: 'system',
        content: enhancedSystemContent,
        timestamp: new Date().toISOString(),
      });
    }

    return enhancedMessages;
  }

  /**
   * Get the latest user message from conversation
   */
  private getLatestUserMessage(messages: ChatMessage[]): string | null {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        return messages[i].content;
      }
    }
    return null;
  }

  /**
   * Determine if a query should use RAG
   */
  private async shouldUseRag(query: string): Promise<boolean> {
    try {
      // Simple heuristics to determine if query is FAQ-suitable
      const lowerQuery = query.toLowerCase();
      
      // FAQ-related keywords
      const faqKeywords = [
        'how', 'what', 'why', 'when', 'where', 'can', 'does', 'is',
        'configure', 'setup', 'install', 'troubleshoot', 'error', 'problem',
        'email', 'password', 'login', 'access', 'permission', 'security',
        'otrs', 'ticket', 'sharepoint', 'teams', 'outlook', 'mfa'
      ];

      // Check if query contains FAQ-related keywords
      const hasKeywords = faqKeywords.some(keyword => lowerQuery.includes(keyword));
      
      // Check query length (too short or too long might not be suitable)
      const isGoodLength = query.length >= 10 && query.length <= 200;
      
      // Check if it's a question (contains question words or ends with ?)
      const isQuestion = lowerQuery.includes('?') || 
                        lowerQuery.startsWith('how ') ||
                        lowerQuery.startsWith('what ') ||
                        lowerQuery.startsWith('why ') ||
                        lowerQuery.startsWith('can ') ||
                        lowerQuery.startsWith('does ');

      return hasKeywords && isGoodLength && isQuestion;

    } catch (error) {
      this.logger.warn('Error determining RAG suitability:', error);
      return true; // Default to using RAG
    }
  }

  /**
   * Get RAG service statistics
   */
  async getRagStatistics() {
    try {
      return await this.ragService.getStatistics();
    } catch (error) {
      this.logger.error('Error getting RAG statistics:', error);
      return null;
    }
  }

  /**
   * Check if RAG service is available
   */
  async isRagAvailable(): Promise<boolean> {
    try {
      return await this.ragService.isAvailable();
    } catch (error) {
      this.logger.warn('RAG availability check failed:', error);
      return false;
    }
  }

  /**
   * Search FAQs directly (for testing/debugging)
   */
  async searchFaqs(query: string, topK = 5): Promise<RelevantFaqResult[]> {
    try {
      return await this.ragService.searchRelevantFaqs(query, topK);
    } catch (error) {
      this.logger.error('Error searching FAQs:', error);
      return [];
    }
  }
}
