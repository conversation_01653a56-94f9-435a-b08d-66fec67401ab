# Azure App Service Deployment Guide for OTRS AI-Powered Backend

## Overview

This guide provides comprehensive instructions for deploying the OTRS AI-Powered backend to Azure App Service using GitHub Actions.

## Prerequisites

1. Azure App Service instance configured for Node.js
2. GitHub repository with proper secrets configured
3. Azure service principal with deployment permissions

## Environment Configuration Strategy

### 1. Environment Files Structure

The backend uses a layered environment configuration:

- `.env.example` - Template with all available variables
- `.env.local` - Development environment (not committed)
- `.env.production` - Production template with placeholders (committed)
- Azure App Service Configuration - Actual production values (secure)

### 2. Required Environment Variables

#### Critical Production Variables (Set via Azure App Service Configuration):

```bash
# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters

# LLM Configuration
LLM_API_KEY=your-azure-ai-api-key
LLM_BASE_URL=https://your-azure-ai-endpoint.com/models

# RAG System
RAG_SYSTEM_URL=https://your-rag-system-url.com

# MCP Server
MCP_SERVER_URL=https://your-mcp-server-url.com

# CORS
CORS_ORIGIN=https://your-frontend-domain.com

# Database (if applicable)
DATABASE_URL=your-database-connection-string
DATABASE_HOST=your-db-host
DATABASE_PORT=5432
DATABASE_USERNAME=your-db-user
DATABASE_PASSWORD=your-db-password
DATABASE_NAME=your-db-name

# Redis (if applicable)
REDIS_URL=redis://your-redis-instance:6379
REDIS_PASSWORD=your-redis-password

# Email (if applicable)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-email-user
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>
```

## Deployment Workflow Features

### 1. Optimizations Implemented

- **Workspace-Aware Build**: Properly handles monorepo structure with shared dependencies
- **Test Skipping**: Bypasses unit tests for faster deployment
- **Dependency Resolution**: Builds shared packages first, then backend
- **Production Dependencies**: Creates deployment package with only production dependencies
- **Yarn Caching**: Uses yarn cache for faster builds
- **Independent Deployment**: Backend can be deployed independently while respecting workspace structure

### 2. Security Best Practices

- Environment variables use placeholder syntax (`${VARIABLE_NAME}`)
- Sensitive values are not committed to repository
- Production configuration is handled via Azure App Service settings

## Azure App Service Configuration

### 1. Application Settings

Configure the following in Azure Portal > App Service > Configuration > Application Settings:

```bash
# Application
NODE_ENV=production
PORT=8080
WEBSITES_PORT=8080

# Your environment variables from the list above
JWT_SECRET=actual-secret-value
LLM_API_KEY=actual-api-key
# ... etc
```

### 2. Startup Command

The workflow automatically sets the startup command to `node dist/main.js`.

### 3. Node.js Version

Ensure your Azure App Service is configured for Node.js 22.x to match the workflow.

## GitHub Secrets Configuration

### Required Secrets

Configure these secrets in GitHub repository settings:

```bash
AZUREAPPSERVICE_CLIENTID_2B8461954F774159977EC1094BEAAC33
AZUREAPPSERVICE_TENANTID_71770D985701468CA89CA3C519C9260C
AZUREAPPSERVICE_SUBSCRIPTIONID_B94CD97FA36043F2A4C0CD2AD435B48C
```

### Optional: Environment Variables as Secrets

For additional security, you can also store sensitive environment variables as GitHub secrets and reference them in the workflow.

## Deployment Process

### 1. Automatic Deployment

The workflow triggers on:

- Push to `feature/rag-with-llm-phi4` branch
- Manual workflow dispatch

### 2. Build Process

1. Checkout code
2. Setup Node.js 22.x with yarn caching (workspace-aware)
3. Install all workspace dependencies: `yarn install --frozen-lockfile`
4. Build shared package first: `yarn turbo run build --filter=@otrs-ai-powered/shared`
5. Build backend package: `yarn turbo run build --filter=@otrs-ai-powered/backend`
6. Install only production dependencies: `yarn install --frozen-lockfile --production`
7. Copy production environment template
8. Create deployment package with workspace node_modules
9. Upload deployment artifact with all resolved dependencies

### 3. Deployment Process

1. Download build artifact
2. Login to Azure using service principal
3. Deploy to Azure App Service with startup command

## Monitoring and Troubleshooting

### 1. Application Logs

Monitor deployment and runtime logs in Azure Portal:

- App Service > Monitoring > Log stream
- App Service > Monitoring > Application Insights (if configured)

### 2. Node.js Version Issues

#### Problem: "The engine 'node' is incompatible with this module"

**Root Cause**: The workflow is using an incompatible Node.js version (e.g., 18.x) when packages require 20.x or higher.

**Solution**:

- Ensure `node-version: "22.x"` is set in the setup-node action
- Use yarn caching: `cache: "yarn"` with `cache-dependency-path: yarn.lock`
- Install workspace dependencies: `yarn install --frozen-lockfile`
- Build shared package first: `yarn turbo run build --filter=@otrs-ai-powered/shared`
- Build backend package: `yarn turbo run build --filter=@otrs-ai-powered/backend`

#### Problem: "Some specified paths were not resolved, unable to cache dependencies"

**Root Cause**: The cache-dependency-path points to a non-existent package-lock.json file.

**Solution**:

- Remove `cache` and `cache-dependency-path` parameters if package-lock.json doesn't exist
- For monorepo with workspace dependencies, install from root first
- Generate package-lock.json in backend package if needed for future caching

#### Problem: Workflow still uses old Node.js version despite configuration

**Solution**:

- Clear GitHub Actions cache in repository settings
- Verify Node.js version in workflow logs with explicit version check step
- Ensure no conflicting Node.js setup steps in the workflow

### 3. Common Issues

#### Environment Variable Issues

- Verify all required variables are set in Azure App Service Configuration
- Check variable names match exactly (case-sensitive)
- Ensure no trailing spaces in values

#### Startup Issues

- Check that `dist/main.js` exists in deployment package
- Verify Node.js version compatibility
- Review application logs for startup errors

#### Build Issues

- Ensure all dependencies are properly listed in package.json
- Check for TypeScript compilation errors
- Verify build script works locally
- For monorepo: ensure turbo build filter targets correct package

#### Module Resolution Issues

**Problem**: "Cannot find module '@nestjs/core'" or similar dependency errors

**Root Cause**: Workspace dependencies (like `@otrs-ai-powered/shared`) are not properly resolved or included in deployment package.

**Solution**:

- Install workspace dependencies from root: `yarn install --frozen-lockfile --production`
- Build shared package before backend: `yarn turbo run build --filter=@otrs-ai-powered/shared`
- Copy workspace `node_modules` to deployment package (contains resolved shared dependencies)
- Ensure deployment package structure includes both `dist/` and `node_modules/` at the same level
- Use `node dist/main` as startup command

**Verification**: Check deployment package contains:

- `dist/main.js` (compiled application)
- `node_modules/@nestjs/core` (and other dependencies)
- `node_modules/@otrs-ai-powered/shared/` (workspace dependency with dist files)
- `node_modules/@otrs-ai-powered/shared/package.json` (shared package metadata)
- `node_modules/@otrs-ai-powered/shared/dist/` (compiled shared package)
- `package.json` with correct start script and workspace dependency reference
- Test module resolution: `node -e "require('@otrs-ai-powered/shared')"`

#### Runtime Module Resolution Issues

**Problem**: "Cannot find module '@otrs-ai-powered/shared'" at runtime in Azure App Service

**Root Cause**: Workspace dependencies are not properly packaged or accessible at runtime.

**Solution**:

- Ensure shared package is built before backend: `yarn turbo run build --filter=@otrs-ai-powered/shared`
- Copy shared package to correct location: `deployment/node_modules/@otrs-ai-powered/shared/`
- Include shared package dist files in deployment
- Create deployment-specific package.json with workspace dependency reference
- Test module resolution in CI: `node -e "require('@otrs-ai-powered/shared')"`

**Verification Steps**:
1. Check `deployment/node_modules/@otrs-ai-powered/shared/package.json` exists
2. Check `deployment/node_modules/@otrs-ai-powered/shared/dist/` contains compiled files
3. Test module resolution works in deployment package
4. Verify Azure App Service can access the shared package at runtime

#### File System Conflicts During Deployment Package Creation

**Problem**: "cp: cannot overwrite non-directory 'deployment/node_modules/@otrs-ai-powered/shared' with directory"

**Root Cause**: Workspace hoisting creates symlinks or files that conflict with directory copying.

**Solution**:
- Check existing target before copying: `test -e deployment/node_modules/@otrs-ai-powered/shared`
- Remove existing files/symlinks: `rm -rf deployment/node_modules/@otrs-ai-powered/shared`
- Use rsync for robust copying: `rsync -av packages/shared/ deployment/node_modules/@otrs-ai-powered/shared/`
- Add error handling and debugging information for copy operations

**Prevention**:
- Always clean target location before copying workspace packages
- Use rsync instead of cp for better handling of existing files
- Add debugging steps to identify file types (file, directory, symlink)

#### Rsync Errors During Shared Package Copying

**Problem**: "rsync: mkdir failed: File exists" when copying shared package

**Root Cause**: Symlinks from workspace hoisting are not properly removed before rsync operation.

**Solution**:
- Verify target is clean after removal: `test -e deployment/node_modules/@otrs-ai-powered/shared`
- Use rsync with --delete flag: `rsync -av --delete packages/shared/ target/`
- Create target directory first: `mkdir -p deployment/node_modules/@otrs-ai-powered/shared`
- Add verification steps after copy operation

**Debugging Steps**:
1. Check if symlink exists: `test -L deployment/node_modules/@otrs-ai-powered/shared`
2. Show symlink target: `readlink deployment/node_modules/@otrs-ai-powered/shared`
3. Force remove symlink: `rm -rf deployment/node_modules/@otrs-ai-powered/shared`
4. Verify clean state: `test ! -e deployment/node_modules/@otrs-ai-powered/shared`

### 3. Health Checks

The application includes environment validation on startup. Check logs for:

- ✅ Environment variables validated successfully
- ❌ Environment validation failed (with specific missing variables)

## Best Practices

### 1. Security

- Never commit real API keys or secrets
- Use Azure Key Vault for highly sensitive data
- Regularly rotate secrets and API keys
- Enable HTTPS only in Azure App Service

### 2. Performance

- Use Application Insights for monitoring
- Configure auto-scaling based on demand
- Monitor memory and CPU usage
- Implement proper logging levels

### 3. Reliability

- Set up health check endpoints
- Configure deployment slots for blue-green deployments
- Implement proper error handling and logging
- Use connection pooling for database connections

## Next Steps

1. Configure all required environment variables in Azure App Service
2. Test the deployment workflow
3. Set up monitoring and alerting
4. Configure custom domain and SSL certificate
5. Implement CI/CD for other environments (staging, etc.)

## Support

For issues with this deployment configuration, check:

1. GitHub Actions workflow logs
2. Azure App Service deployment logs
3. Application runtime logs
4. Environment variable configuration
