import { User, TokenResponse, AUTH_API } from '@otrs-ai-powered/shared';
import { apiService } from './api';

export const authService = {
  /**
   * Login with username and password
   */
  login: async (username: string, password: string): Promise<TokenResponse> => {
    return apiService.post<TokenResponse>(AUTH_API.LOGIN, { username, password });
  },

  /**
   * Logout the current user
   */
  logout: async (): Promise<void> => {
    return apiService.post<void>(AUTH_API.LOGOUT);
  },

  /**
   * Refresh the access token using a refresh token
   */
  refreshToken: async (refreshToken: string): Promise<TokenResponse> => {
    return apiService.post<TokenResponse>(AUTH_API.REFRESH_TOKEN, { refreshToken });
  },

  /**
   * Get the current user's information
   */
  getCurrentUser: async (): Promise<User> => {
    return apiService.get<User>(AUTH_API.ME);
  },
};
