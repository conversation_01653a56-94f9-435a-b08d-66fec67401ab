import { registerAs } from '@nestjs/config';
import {
  getRequiredEnvVar,
  getRequiredEnvNumber,
  getRequiredEnvFloat
} from './environment.validation';

export default registerAs('llm', () => ({
  provider: getRequiredEnvVar('LLM_PROVIDER', 'LLM provider type'),
  apiKey: getRequiredEnvVar('LLM_API_KEY', 'LLM API key'),
  baseUrl: getRequiredEnvVar('LLM_BASE_URL', 'LLM base URL'),
  model: getRequiredEnvVar('LLM_MODEL', 'LLM model name'),
  temperature: getRequiredEnvFloat('LLM_TEMPERATURE', 'LLM temperature'),
  maxTokens: getRequiredEnvNumber('LLM_MAX_TOKENS', 'LLM max tokens'),
  timeout: getRequiredEnvNumber('LLM_TIMEOUT', 'LLM request timeout'),
}));
