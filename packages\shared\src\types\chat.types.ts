/**
 * Represents a chat message in the system
 */
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Represents a chat session
 */
export interface ChatSession {
  id: string;
  userId: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

/**
 * Represents a chat completion request to the LLM
 */
export interface ChatCompletionRequest {
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  tools?: Tool[];
}

/**
 * Represents a tool that can be called by the LLM
 */
export interface Tool {
  type: string;
  name: string;
  description: string;
  parameters: Record<string, any>;
}

/**
 * Represents a tool call made by the LLM
 */
export interface ToolCall {
  toolId: string;
  toolName: string;
  arguments: Record<string, any>;
}

/**
 * Represents the result of a tool call
 */
export interface ToolCallResult {
  toolCallId: string;
  result: any;
  error?: string;
}
