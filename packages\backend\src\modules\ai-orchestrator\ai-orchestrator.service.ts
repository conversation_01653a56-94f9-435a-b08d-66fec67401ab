import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import {
  IAiOrchestratorService,
  AiResponse,
  ResponseType,
  ToolExecutionResult,
  QueryAnalysis,
  QueryIntent,
  AiOrchestratorConfig,
} from './interfaces/ai-orchestrator.interface';
import { IRagService, RelevantFaqResult } from '../rag/interfaces/rag.interface';
import { LlmService } from '../llm/llm.service';
import { ToolCallingService } from '../tool-calling/tool-calling.service';

@Injectable()
export class AiOrchestratorService implements IAiOrchestratorService {
  private readonly logger = new Logger(AiOrchestratorService.name);
  private readonly config: AiOrchestratorConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly llmService: LlmService,
    private readonly toolCallingService: ToolCallingService,
    // Note: RAG service will be injected when implemented
    // private readonly ragService: IRagService,
  ) {
    this.config = {
      ragEnabled: this.configService.get<boolean>('ai.ragEnabled', true),
      mcpEnabled: this.configService.get<boolean>('ai.mcpEnabled', true),
      ragThreshold: this.configService.get<number>('ai.ragThreshold', 0.7),
      maxRagResults: this.configService.get<number>('ai.maxRagResults', 5),
      toolExecutionTimeout: this.configService.get<number>('ai.toolExecutionTimeout', 30000),
      hybridModeEnabled: this.configService.get<boolean>('ai.hybridModeEnabled', true),
    };
  }

  async processQuery(
    query: string,
    conversationHistory: ChatMessage[],
    availableTools?: Tool[]
  ): Promise<AiResponse> {
    try {
      this.logger.debug(`Processing query: ${query.substring(0, 100)}...`);

      // Analyze the query to determine intent and requirements
      const analysis = await this.analyzeQuery(query, conversationHistory);
      
      this.logger.debug(`Query analysis: intent=${analysis.intent}, requiresRag=${analysis.requiresRag}, requiresTools=${analysis.requiresTools}`);

      // Route to appropriate processing strategy
      if (analysis.requiresRag && analysis.requiresTools && this.config.hybridModeEnabled) {
        return this.processHybridQuery(query, conversationHistory, availableTools);
      } else if (analysis.requiresRag && this.config.ragEnabled) {
        return this.processRagQuery(query, conversationHistory);
      } else if (analysis.requiresTools && this.config.mcpEnabled) {
        return this.processToolQuery(query, conversationHistory, availableTools);
      } else {
        return this.processDirectQuery(query, conversationHistory);
      }
    } catch (error) {
      this.logger.error('Error processing query:', error);
      
      // Fallback to direct LLM response
      return this.processDirectQuery(query, conversationHistory);
    }
  }

  async executeTools(toolCalls: ToolCall[]): Promise<ToolExecutionResult[]> {
    try {
      this.logger.debug(`Executing ${toolCalls.length} tool calls`);

      const results = await Promise.allSettled(
        toolCalls.map(async (toolCall) => {
          const startTime = Date.now();
          
          try {
            const result = await this.toolCallingService.executeToolCall(toolCall);
            const executionTime = Date.now() - startTime;
            
            return {
              toolCallId: toolCall.toolId,
              success: !result.error,
              result: result.result,
              error: result.error,
              executionTime,
            };
          } catch (error) {
            const executionTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            return {
              toolCallId: toolCall.toolId,
              success: false,
              error: errorMessage,
              executionTime,
            };
          }
        })
      );

      return results.map((result) => 
        result.status === 'fulfilled' ? result.value : {
          toolCallId: 'unknown',
          success: false,
          error: 'Tool execution failed',
          executionTime: 0,
        }
      );
    } catch (error) {
      this.logger.error('Error executing tools:', error);
      throw error;
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Check if core LLM service is available
      const llmAvailable = await this.llmService.isAvailable();
      
      // Check tool calling service if MCP is enabled
      let toolsAvailable = true;
      if (this.config.mcpEnabled) {
        toolsAvailable = await this.toolCallingService.isAvailable();
      }

      // TODO: Check RAG service when implemented
      // let ragAvailable = true;
      // if (this.config.ragEnabled) {
      //   ragAvailable = await this.ragService.isAvailable();
      // }

      return llmAvailable && toolsAvailable;
    } catch (error) {
      this.logger.error('Error checking service availability:', error);
      return false;
    }
  }

  /**
   * Analyze query to determine processing requirements
   */
  private async analyzeQuery(query: string, conversationHistory: ChatMessage[]): Promise<QueryAnalysis> {
    // Simple heuristic-based analysis
    // In a production system, this could use a separate ML model
    
    const queryLower = query.toLowerCase();
    
    // Check for information-seeking patterns
    const infoKeywords = ['what', 'how', 'why', 'when', 'where', 'explain', 'tell me', 'help'];
    const hasInfoKeywords = infoKeywords.some(keyword => queryLower.includes(keyword));
    
    // Check for action/task patterns
    const actionKeywords = ['create', 'update', 'delete', 'send', 'make', 'generate', 'add', 'remove'];
    const hasActionKeywords = actionKeywords.some(keyword => queryLower.includes(keyword));
    
    // Check for ticket-related patterns
    const ticketKeywords = ['ticket', 'issue', 'problem', 'bug', 'request'];
    const hasTicketKeywords = ticketKeywords.some(keyword => queryLower.includes(keyword));

    let intent: QueryIntent;
    let requiresRag = false;
    let requiresTools = false;

    if (hasActionKeywords && hasTicketKeywords) {
      intent = QueryIntent.TASK_EXECUTION;
      requiresTools = true;
    } else if (hasInfoKeywords) {
      intent = QueryIntent.INFORMATION_SEEKING;
      requiresRag = true;
    } else if (hasTicketKeywords) {
      intent = QueryIntent.TROUBLESHOOTING;
      requiresRag = true;
      requiresTools = hasActionKeywords;
    } else {
      intent = QueryIntent.CONVERSATIONAL;
    }

    return {
      intent,
      requiresRag,
      requiresTools,
      confidence: 0.8, // Simple confidence score
      keywords: queryLower.split(' ').filter(word => word.length > 3),
    };
  }

  /**
   * Process query using RAG only
   */
  private async processRagQuery(query: string, conversationHistory: ChatMessage[]): Promise<AiResponse> {
    this.logger.debug('Processing RAG query');
    
    // TODO: Implement RAG search when RAG service is available
    // const ragResults = await this.ragService.searchRelevantFaqs(query, this.config.maxRagResults);
    // const filteredResults = ragResults.filter(result => result.similarityScore >= this.config.ragThreshold);
    
    // For now, fallback to direct LLM response
    const response = await this.llmService.generateResponse(conversationHistory);
    
    return {
      content: response.content,
      responseType: ResponseType.DIRECT_LLM, // Will be RAG_BASED when implemented
      confidence: 0.8,
    };
  }

  /**
   * Process query using tools only
   */
  private async processToolQuery(
    query: string,
    conversationHistory: ChatMessage[],
    availableTools?: Tool[]
  ): Promise<AiResponse> {
    this.logger.debug('Processing tool query');
    
    const response = await this.llmService.generateResponse(conversationHistory, availableTools);
    
    return {
      content: response.content,
      responseType: ResponseType.TOOL_CALLING,
      toolCalls: response.toolCalls,
      confidence: 0.9,
    };
  }

  /**
   * Process query using both RAG and tools
   */
  private async processHybridQuery(
    query: string,
    conversationHistory: ChatMessage[],
    availableTools?: Tool[]
  ): Promise<AiResponse> {
    this.logger.debug('Processing hybrid query');
    
    // TODO: Implement hybrid processing when RAG service is available
    // For now, prioritize tool calling
    return this.processToolQuery(query, conversationHistory, availableTools);
  }

  /**
   * Process query using direct LLM only
   */
  private async processDirectQuery(query: string, conversationHistory: ChatMessage[]): Promise<AiResponse> {
    this.logger.debug('Processing direct query');
    
    const response = await this.llmService.generateResponse(conversationHistory);
    
    return {
      content: response.content,
      responseType: ResponseType.DIRECT_LLM,
      confidence: 0.7,
    };
  }
}
