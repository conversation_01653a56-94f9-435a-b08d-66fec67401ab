version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: packages/frontend/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VITE_API_URL=http://backend:4000
      - VITE_WEBSOCKET_URL=http://backend:4000
    depends_on:
      - backend
    restart: unless-stopped

  backend:
    build:
      context: .
      dockerfile: packages/backend/Dockerfile
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - PORT=4000
      - CORS_ORIGIN=http://frontend:3000
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRATION=${JWT_EXPIRATION}
      - JWT_REFRESH_EXPIRATION=${JWT_REFRESH_EXPIRATION}
      - LLM_PROVIDER=${LLM_PROVIDER}
      - LLM_API_KEY=${LLM_API_KEY}
      - LLM_MODEL=${LLM_MODEL}
      - LLM_TEMPERATURE=${LLM_TEMPERATURE}
      - LLM_MAX_TOKENS=${LLM_MAX_TOKENS}
      - LLM_TIMEOUT=${LLM_TIMEOUT}
      - MCP_SERVER_URL=http://mcp-server:5000
      - RAG_SYSTEM_URL=http://rag-system:6000
    depends_on:
      - mcp-server
      - rag-system
    restart: unless-stopped

  mcp-server:
    build:
      context: .
      dockerfile: packages/mcp-server/Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - OTRS_API_URL=${OTRS_API_URL}
      - OTRS_API_USERNAME=${OTRS_API_USERNAME}
      - OTRS_API_PASSWORD=${OTRS_API_PASSWORD}
    restart: unless-stopped

  rag-system:
    build:
      context: .
      dockerfile: packages/rag-system/Dockerfile
    ports:
      - "6000:6000"
    environment:
      - NODE_ENV=production
      - PORT=6000
      - EMBEDDING_API_KEY=${EMBEDDING_API_KEY}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL}
      - EMBEDDING_DIMENSIONS=${EMBEDDING_DIMENSIONS}
      - VECTOR_DB_URL=${VECTOR_DB_URL}
      - VECTOR_DB_API_KEY=${VECTOR_DB_API_KEY}
    restart: unless-stopped

volumes:
  vector_db_data:
    driver: local
