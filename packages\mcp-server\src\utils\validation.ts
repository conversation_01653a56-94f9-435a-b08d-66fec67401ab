/**
 * Validates that required fields are present in the input object
 * @param input Input object to validate
 * @param requiredFields Array of required field names
 * @throws Error if any required field is missing
 */
export function validateRequiredFields(input: Record<string, any>, requiredFields: string[]): void {
  const missingFields = requiredFields.filter(field => input[field] === undefined);
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
  }
}

/**
 * Validates that a string field meets minimum and maximum length requirements
 * @param value String value to validate
 * @param fieldName Name of the field (for error messages)
 * @param minLength Minimum length (optional)
 * @param maxLength Maximum length (optional)
 * @throws Error if validation fails
 */
export function validateStringLength(
  value: string,
  fieldName: string,
  minLength?: number,
  maxLength?: number
): void {
  if (typeof value !== 'string') {
    throw new Error(`${fieldName} must be a string`);
  }
  
  if (minLength !== undefined && value.length < minLength) {
    throw new Error(`${fieldName} must be at least ${minLength} characters long`);
  }
  
  if (maxLength !== undefined && value.length > maxLength) {
    throw new Error(`${fieldName} must be at most ${maxLength} characters long`);
  }
}

/**
 * Validates that a value is one of the allowed values
 * @param value Value to validate
 * @param fieldName Name of the field (for error messages)
 * @param allowedValues Array of allowed values
 * @throws Error if validation fails
 */
export function validateEnum<T>(
  value: T,
  fieldName: string,
  allowedValues: T[]
): void {
  if (!allowedValues.includes(value)) {
    throw new Error(`${fieldName} must be one of: ${allowedValues.join(', ')}`);
  }
}
