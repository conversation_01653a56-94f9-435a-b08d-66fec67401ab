#!/usr/bin/env ts-node

/**
 * Embedding Service Diagnostic Tool
 * 
 * This script performs comprehensive testing of the embedding service,
 * including connectivity, quality, and semantic discrimination validation.
 * 
 * Usage: npx ts-node scripts/diagnose-embedding-service.ts
 */

import { EmbeddingService, EmbeddingConfig } from '../src/embeddings/embedding.service';
import { logger } from '../src/utils/logger';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.test') });

async function diagnoseEmbeddingService() {
  try {
    logger.info('🔬 Embedding Service Diagnostic');
    logger.info('===============================');

    // Configuration
    const config: EmbeddingConfig = {
      provider: 'vllm',
      apiKey: process.env.EMBEDDING_API_KEY || 'test-key',
      model: process.env.EMBEDDING_MODEL || 'sentence-transformers/all-MiniLM-L6-v2',
      baseUrl: process.env.EMBEDDING_BASE_URL,
      dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '384'),
      batchSize: 10,
      timeout: 30000,
    };

    logger.info('📋 Configuration:');
    logger.info(`   Provider: ${config.provider}`);
    logger.info(`   Model: ${config.model}`);
    logger.info(`   Base URL: ${config.baseUrl}`);
    logger.info(`   Dimensions: ${config.dimensions}`);
    logger.info(`   API Key: ${config.apiKey.substring(0, 10)}...`);

    const embeddingService = new EmbeddingService(config);

    // Test 1: Service Information
    logger.info('\n📊 Test 1: Service Information');
    logger.info('==============================');
    const serviceInfo = embeddingService.getServiceInfo();
    logger.info('✅ Service Info:', serviceInfo);

    // Test 2: Service Availability
    logger.info('\n🔗 Test 2: Service Availability');
    logger.info('===============================');
    try {
      const isAvailable = await embeddingService.isAvailable();
      if (isAvailable) {
        logger.info('✅ Service is available and responding');
      } else {
        logger.warn('⚠️ Service is not available');
      }
    } catch (error) {
      logger.error('❌ Service availability check failed:', error instanceof Error ? error.message : String(error));
    }

    // Test 3: Single Embedding Generation
    logger.info('\n🧪 Test 3: Single Embedding Generation');
    logger.info('======================================');
    try {
      const testText = 'How do I configure email settings in OTRS?';
      logger.info(`Testing with: "${testText}"`);
      
      const startTime = Date.now();
      const result = await embeddingService.generateEmbedding(testText);
      const responseTime = Date.now() - startTime;
      
      logger.info('✅ Single Embedding Success!');
      logger.info(`   Dimensions: ${result.embedding.length}`);
      logger.info(`   Token Count: ${result.tokenCount}`);
      logger.info(`   Model: ${result.model}`);
      logger.info(`   Response Time: ${responseTime}ms`);
      logger.info(`   Sample Values: [${result.embedding.slice(0, 5).map((v: number) => v.toFixed(4)).join(', ')}...]`);

      // Validate dimensions
      if (result.embedding.length === config.dimensions) {
        logger.info('✅ Dimensions match configuration');
      } else {
        logger.error(`❌ Dimension mismatch: expected ${config.dimensions}, got ${result.embedding.length}`);
      }

    } catch (error) {
      logger.error('❌ Single embedding generation failed:', error instanceof Error ? error.message : String(error));
    }

    // Test 4: Batch Embedding Generation
    logger.info('\n📦 Test 4: Batch Embedding Generation');
    logger.info('====================================');
    try {
      const testTexts = [
        'Email configuration help',
        'SharePoint file access',
        'Teams meeting setup',
        'Password reset guide',
        'MFA authentication',
      ];

      logger.info(`Testing with ${testTexts.length} texts`);
      
      const startTime = Date.now();
      const batchResult = await embeddingService.generateBatchEmbeddings(testTexts);
      const responseTime = Date.now() - startTime;
      
      logger.info('✅ Batch Embedding Success!');
      logger.info(`   Processed: ${batchResult.processedCount}`);
      logger.info(`   Failed: ${batchResult.failedCount}`);
      logger.info(`   Total Tokens: ${batchResult.totalTokens}`);
      logger.info(`   Total Time: ${responseTime}ms`);
      logger.info(`   Average Time per Embedding: ${(responseTime / batchResult.processedCount).toFixed(0)}ms`);

      // Validate each embedding
      batchResult.embeddings.forEach((embedding, index) => {
        if (embedding.length !== config.dimensions) {
          logger.error(`❌ Embedding ${index + 1} has wrong dimensions: ${embedding.length}`);
        }
      });

      if (batchResult.failedCount === 0) {
        logger.info('✅ All batch embeddings generated successfully');
      } else {
        logger.warn(`⚠️ ${batchResult.failedCount} embeddings failed`);
      }

    } catch (error) {
      logger.error('❌ Batch embedding generation failed:', error instanceof Error ? error.message : String(error));
    }

    // Test 5: Semantic Quality Assessment
    logger.info('\n🎯 Test 5: Semantic Quality Assessment');
    logger.info('=====================================');
    try {
      const semanticTests = [
        { text1: 'How do I configure email settings?', text2: 'Email configuration and setup', expected: 'HIGH' },
        { text1: 'How do I configure email settings?', text2: 'SharePoint file management', expected: 'MEDIUM' },
        { text1: 'How do I configure email settings?', text2: 'Cooking recipes and food', expected: 'LOW' },
      ];

      const embeddings = [];
      for (const test of semanticTests) {
        const emb1 = await embeddingService.generateEmbedding(test.text1);
        const emb2 = await embeddingService.generateEmbedding(test.text2);
        embeddings.push({ ...test, emb1: emb1.embedding, emb2: emb2.embedding });
      }

      logger.info('Semantic Similarity Results:');
      let discriminationScore = 0;
      
      for (const test of embeddings) {
        const similarity = calculateCosineSimilarity(test.emb1, test.emb2);
        logger.info(`   "${test.text1.substring(0, 30)}..." vs "${test.text2.substring(0, 30)}..."`);
        logger.info(`     Similarity: ${similarity.toFixed(4)} (Expected: ${test.expected})`);
        
        if (test.expected === 'HIGH' && similarity > 0.6) {
          logger.info('     ✅ Good high similarity');
        } else if (test.expected === 'MEDIUM' && similarity > 0.3 && similarity < 0.7) {
          logger.info('     ✅ Good medium similarity');
        } else if (test.expected === 'LOW' && similarity < 0.4) {
          logger.info('     ✅ Good low similarity');
        } else {
          logger.warn(`     ⚠️ Unexpected similarity for ${test.expected} expectation`);
        }
      }

      // Calculate discrimination score
      const highSim = embeddings.find(e => e.expected === 'HIGH');
      const lowSim = embeddings.find(e => e.expected === 'LOW');
      
      if (highSim && lowSim) {
        const highScore = calculateCosineSimilarity(highSim.emb1, highSim.emb2);
        const lowScore = calculateCosineSimilarity(lowSim.emb1, lowSim.emb2);
        discriminationScore = highScore - lowScore;
        
        logger.info(`\n📊 Discrimination Score: ${discriminationScore.toFixed(4)}`);
        
        if (discriminationScore > 0.3) {
          logger.info('✅ Excellent semantic discrimination - suitable for RAG');
        } else if (discriminationScore > 0.2) {
          logger.info('✅ Good semantic discrimination');
        } else {
          logger.warn('⚠️ Poor semantic discrimination - may affect search quality');
        }
      }

    } catch (error) {
      logger.error('❌ Semantic quality assessment failed:', error instanceof Error ? error.message : String(error));
    }

    // Test 6: Performance Benchmark
    logger.info('\n⚡ Test 6: Performance Benchmark');
    logger.info('===============================');
    try {
      const benchmarkTexts = Array(10).fill(0).map((_, i) => `Test embedding performance ${i + 1}`);
      
      const startTime = Date.now();
      const batchResult = await embeddingService.generateBatchEmbeddings(benchmarkTexts);
      const totalTime = Date.now() - startTime;

      const tokensPerSecond = (batchResult.totalTokens / (totalTime / 1000)).toFixed(1);
      const embeddingsPerSecond = (batchResult.processedCount / (totalTime / 1000)).toFixed(1);

      logger.info('✅ Performance Benchmark Results:');
      logger.info(`   Total Time: ${totalTime}ms`);
      logger.info(`   Embeddings per Second: ${embeddingsPerSecond}`);
      logger.info(`   Tokens per Second: ${tokensPerSecond}`);
      logger.info(`   Average Time per Embedding: ${(totalTime / batchResult.processedCount).toFixed(0)}ms`);

      if (totalTime / batchResult.processedCount < 500) {
        logger.info('✅ Excellent performance - under 500ms per embedding');
      } else if (totalTime / batchResult.processedCount < 1000) {
        logger.info('✅ Good performance - under 1s per embedding');
      } else {
        logger.warn('⚠️ Slow performance - over 1s per embedding');
      }

    } catch (error) {
      logger.error('❌ Performance benchmark failed:', error instanceof Error ? error.message : String(error));
    }

    logger.info('\n🎉 Embedding Service Diagnostic Complete!');

  } catch (error) {
    logger.error('❌ Embedding service diagnostic failed:', error);
    process.exit(1);
  }
}

/**
 * Calculate cosine similarity between two vectors
 */
function calculateCosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same dimensions');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
}

if (require.main === module) {
  diagnoseEmbeddingService();
}
