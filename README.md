# OTRS AI-Powered Chat Assistant

A comprehensive AI Chat Assistant that integrates with OTRS (Open Ticket Request System), providing intelligent responses and ticket management capabilities.

## Project Overview

This project implements a chat assistant that can be embedded within the OTRS interface, allowing users to interact with an AI assistant that can answer questions and create tickets when necessary.

### Key Components

1. **Frontend Client (React TypeScript)**
   - Collapsible chat widget for embedding in OTRS
   - Modern, responsive UI with typing indicators and message history
   - WebSocket integration for real-time communication

2. **Backend API Server (NestJS)**
   - RESTful API endpoints for chat communication
   - Integration with LLM services
   - Authentication and authorization
   - WebSocket support for real-time updates

3. **MCP Server (TypeScript)**
   - Built on MCP SDK for tool integration
   - Implementation of OTRS ticket creation tools
   - Function calling architecture for the LLM

4. **Model Tuning Project**
   - Structure for fine-tuning LLM models with domain-specific data
   - Dataset preparation and validation
   - Training pipeline and evaluation metrics

5. **RAG (Retrieval Augmented Generation) System**
   - Vector database integration for storing FAQs and knowledge base
   - Embedding generation and storage
   - Retrieval mechanisms for enhancing LLM responses

## Project Structure

```
otrs-ai-powered/
├── packages/                      # Monorepo structure using workspaces
│   ├── frontend/                  # React TypeScript frontend
│   ├── backend/                   # NestJS backend API server
│   ├── mcp-server/                # MCP Server for tool integration
│   ├── model-tuning/              # Model tuning project
│   ├── rag-system/                # RAG system implementation
│   └── shared/                    # Shared types, utilities, and constants
├── docs/                          # Project documentation
├── scripts/                       # Development and deployment scripts
└── .github/                       # GitHub workflows for CI/CD
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Yarn (v1.22 or later)
- Python 3.8+ (for model tuning)
- Docker (optional, for containerized deployment)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/otrs-ai-powered.git
   cd otrs-ai-powered
   ```

2. Install dependencies:
   ```bash
   yarn install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. Build all packages:
   ```bash
   yarn build
   ```

### Development

1. Start all services in development mode:
   ```bash
   yarn dev
   ```

2. Or start individual services:
   ```bash
   # Frontend
   yarn workspace @otrs-ai-powered/frontend dev
   
   # Backend
   yarn workspace @otrs-ai-powered/backend dev
   
   # MCP Server
   yarn workspace @otrs-ai-powered/mcp-server dev
   
   # RAG System
   yarn workspace @otrs-ai-powered/rag-system dev
   ```

### Testing

Run tests for all packages:
```bash
yarn test
```

Or test individual packages:
```bash
yarn workspace @otrs-ai-powered/frontend test
```

## Deployment

### Production Build

```bash
yarn build
```

### Docker Deployment

```bash
docker-compose up -d
```

## Documentation

For detailed documentation on each component, see the [docs](./docs) directory.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
