import { Pinecone } from '@pinecone-database/pinecone';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

async function testIndexReadiness() {
  console.log('🔍 Testing Pinecone Index Readiness...\n');

  const apiKey = process.env.PINECONE_API_KEY;
  const indexName = process.env.PINECONE_INDEX_NAME;

  if (!apiKey || !indexName) {
    console.error('❌ Missing required environment variables');
    process.exit(1);
  }

  try {
    const pinecone = new Pinecone({ apiKey });

    // Test 1: Check if index exists
    console.log('📝 Test 1: Checking if index exists...');
    const indexList = await pinecone.listIndexes();
    const indexExists = indexList.indexes?.some(index => index.name === indexName);
    
    if (!indexExists) {
      console.log(`❌ Index "${indexName}" does not exist`);
      process.exit(1);
    }
    
    console.log(`✅ Index "${indexName}" exists`);

    // Test 2: Try to get index description
    console.log('\n📝 Test 2: Getting index description...');
    try {
      const indexDescription = await pinecone.describeIndex(indexName);
      console.log(`✅ Index description retrieved:`);
      console.log(`  Name: ${indexDescription.name}`);
      console.log(`  Dimension: ${indexDescription.dimension}`);
      console.log(`  Metric: ${indexDescription.metric}`);
      console.log(`  Status: ${indexDescription.status?.ready ? 'Ready' : 'Not Ready'}`);
      console.log(`  State: ${indexDescription.status?.state || 'Unknown'}`);
      
      if (indexDescription.status?.ready) {
        console.log('🎉 Index is ready for operations!');
      } else {
        console.log('⏳ Index is not yet ready for operations');
      }
    } catch (error) {
      console.error(`❌ Failed to get index description:`, error instanceof Error ? error.message : String(error));
    }

    // Test 3: Try to get index stats
    console.log('\n📝 Test 3: Getting index statistics...');
    try {
      const index = pinecone.index(indexName);
      const stats = await index.describeIndexStats();
      console.log(`✅ Index stats retrieved successfully:`);
      console.log(`  Total records: ${stats.totalRecordCount || 0}`);
      console.log(`  Index fullness: ${stats.indexFullness || 0}`);
      console.log(`  Dimension: ${stats.dimension || 'unknown'}`);
      
      if (stats.namespaces) {
        console.log(`  Namespaces: ${Object.keys(stats.namespaces).length}`);
      }
    } catch (error) {
      console.error(`❌ Failed to get index stats:`, error instanceof Error ? error.message : String(error));
      console.log('This might indicate the index is not ready yet');
    }

    // Test 4: Try a simple upsert operation
    console.log('\n📝 Test 4: Testing upsert operation...');
    try {
      const index = pinecone.index(indexName);
      const testVector = Array.from({ length: 384 }, () => Math.random());
      
      await index.upsert([{
        id: 'test-vector-' + Date.now(),
        values: testVector,
        metadata: { test: true, timestamp: new Date().toISOString() }
      }]);
      
      console.log('✅ Test upsert operation successful - index is ready!');
    } catch (error) {
      console.error(`❌ Test upsert failed:`, error instanceof Error ? error.message : String(error));
      console.log('This indicates the index is not ready for operations yet');
    }

    console.log('\n🎉 Index readiness test completed!');

  } catch (error) {
    console.error('\n❌ Index readiness test failed:');
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run the test
testIndexReadiness().catch(console.error);
