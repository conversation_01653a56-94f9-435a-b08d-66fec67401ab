# RAG System Documentation

This directory contains documentation for the RAG (Retrieval Augmented Generation) System component of the OTRS AI-Powered Chat Assistant.

## Overview

The RAG System is built with TypeScript and provides a way to enhance LLM responses with domain-specific knowledge. It includes embedding generation, vector storage, and retrieval mechanisms.

## Key Features

- Document indexing and chunking
- Embedding generation
- Vector database integration
- Similarity search
- Hybrid search (vector + keyword)

## Directory Structure

```
packages/rag-system/
├── src/
│   ├── index.ts                   # Entry point
│   ├── config/                    # Configuration
│   │   └── vector-db.config.ts    # Vector database configuration
│   ├── embeddings/                # Embedding generation
│   │   ├── embedding.service.ts   # Embedding service
│   │   └── models/                # Embedding models
│   ├── storage/                   # Vector storage
│   │   ├── vector-db.service.ts   # Vector database service
│   │   └── adapters/              # Database adapters
│   ├── retrieval/                 # Retrieval mechanisms
│   │   ├── retriever.service.ts   # Retrieval service
│   │   └── strategies/            # Retrieval strategies
│   ├── indexing/                  # Indexing utilities
│   │   ├── indexer.service.ts     # Indexing service
│   │   └── processors/            # Document processors
│   └── types/                     # TypeScript type definitions
└── ...
```

## Getting Started

### Development

```bash
# Start the development server
yarn workspace @otrs-ai-powered/rag-system dev
```

### Building

```bash
# Build for production
yarn workspace @otrs-ai-powered/rag-system build
```

### Testing

```bash
# Run tests
yarn workspace @otrs-ai-powered/rag-system test
```

## API Documentation

The RAG System provides the following API endpoints:

### Query

- `POST /api/query`: Query the RAG system for relevant documents

Request body:
```json
{
  "query": "How do I reset my password?",
  "topK": 5
}
```

Response:
```json
{
  "results": [
    {
      "document": {
        "id": "doc-1",
        "content": "To reset your password, go to the login page and click on 'Forgot Password'...",
        "metadata": {
          "source": "faq",
          "title": "Password Reset Guide",
          "category": "authentication"
        }
      },
      "score": 0.92
    },
    {
      "document": {
        "id": "doc-2",
        "content": "Password reset instructions are sent to your email address...",
        "metadata": {
          "source": "user-manual",
          "title": "User Authentication",
          "category": "security"
        }
      },
      "score": 0.85
    }
  ]
}
```

### Index

- `POST /api/index`: Index documents in the RAG system

Request body:
```json
{
  "documents": [
    {
      "id": "doc-1",
      "content": "To reset your password, go to the login page and click on 'Forgot Password'...",
      "metadata": {
        "source": "faq",
        "title": "Password Reset Guide",
        "category": "authentication"
      }
    }
  ]
}
```

Response:
```json
{
  "success": true,
  "count": 1
}
```

### Health Check

- `GET /health`: Check the health of the RAG System

## Document Processing

The RAG System processes documents in the following steps:

1. **Chunking**: Split long documents into manageable chunks
2. **Embedding**: Generate vector embeddings for each chunk
3. **Indexing**: Store the chunks and embeddings in the vector database

## Retrieval

The RAG System supports various retrieval strategies:

- **Similarity Search**: Find documents similar to the query
- **Hybrid Search**: Combine vector search with keyword search
- **Filtered Search**: Apply metadata filters to search results

## Vector Database

The RAG System can integrate with various vector databases:

- **In-Memory**: Simple in-memory vector store (for development)
- **Pinecone**: Cloud-based vector database
- **Weaviate**: Knowledge graph and vector search engine
- **Milvus**: Open-source vector database

## Embedding Models

The RAG System supports various embedding models:

- **OpenAI**: text-embedding-ada-002
- **Hugging Face**: Sentence transformers
- **Custom**: Custom embedding models
