apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-server
  labels:
    app: mcp-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: mcp-server
  template:
    metadata:
      labels:
        app: mcp-server
    spec:
      containers:
      - name: mcp-server
        image: your-org/otrs-ai-powered:latest-mcp-server
        ports:
        - containerPort: 5000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "5000"
        - name: OTRS_API_URL
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: OTRS_API_URL
        - name: OTRS_API_USERNAME
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: OTRS_API_USERNAME
        - name: OTRS_API_PASSWORD
          valueFrom:
            secretKeyRef:
              name: otrs-ai-secrets
              key: OTRS_API_PASSWORD
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: mcp-server
spec:
  selector:
    app: mcp-server
  ports:
  - port: 5000
    targetPort: 5000
  type: ClusterIP
