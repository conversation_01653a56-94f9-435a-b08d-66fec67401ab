import { Pinecone } from '@pinecone-database/pinecone';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

async function testPineconeConnection() {
  console.log('🔍 Testing Pinecone Connection...\n');

  // Check environment variables
  const apiKey = process.env.PINECONE_API_KEY;
  const indexName = process.env.PINECONE_INDEX_NAME;
  const dimension = parseInt(process.env.PINECONE_DIMENSION || '384', 10);

  console.log('📋 Configuration:');
  console.log(`  API Key: ${apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT SET'}`);
  console.log(`  Index Name: ${indexName || 'NOT SET'}`);
  console.log(`  Dimension: ${dimension}`);
  console.log('');

  if (!apiKey) {
    console.error('❌ PINECONE_API_KEY is not set');
    process.exit(1);
  }

  if (!indexName) {
    console.error('❌ PINECONE_INDEX_NAME is not set');
    process.exit(1);
  }

  try {
    // Initialize Pinecone client
    console.log('🔌 Initializing Pinecone client...');
    const pinecone = new Pinecone({
      apiKey: apiKey,
    });

    // Test 1: List indexes
    console.log('📝 Test 1: Listing indexes...');
    const indexList = await pinecone.listIndexes();
    console.log(`✅ Successfully connected to Pinecone`);
    console.log(`📊 Found ${indexList.indexes?.length || 0} indexes:`);
    
    if (indexList.indexes && indexList.indexes.length > 0) {
      indexList.indexes.forEach((index, i) => {
        console.log(`  ${i + 1}. ${index.name} (${index.dimension}D, ${index.metric})`);
      });
    } else {
      console.log('  No indexes found');
    }
    console.log('');

    // Test 2: Check if our specific index exists
    console.log(`🎯 Test 2: Checking for index "${indexName}"...`);
    const indexExists = indexList.indexes?.some(index => index.name === indexName);
    
    if (indexExists) {
      console.log(`✅ Index "${indexName}" exists`);
      
      // Test 3: Get index stats
      console.log(`📈 Test 3: Getting index statistics...`);
      try {
        const index = pinecone.index(indexName);
        const stats = await index.describeIndexStats();
        console.log(`✅ Index stats retrieved successfully:`);
        console.log(`  Total vectors: ${stats.totalRecordCount || 0}`);
        console.log(`  Index fullness: ${stats.indexFullness || 0}`);
        console.log(`  Dimension: ${stats.dimension || 'unknown'}`);

        if (stats.namespaces) {
          console.log(`  Namespaces: ${Object.keys(stats.namespaces).length}`);
        }
      } catch (error) {
        console.error(`❌ Failed to get index stats:`, error instanceof Error ? error.message : String(error));
      }
    } else {
      console.log(`⚠️  Index "${indexName}" does not exist`);
      console.log(`💡 This is normal for first-time setup. The system will create it automatically.`);
      
      // Test 3: Try to create the index
      console.log(`🏗️  Test 3: Attempting to create index "${indexName}"...`);
      try {
        await pinecone.createIndex({
          name: indexName,
          dimension: dimension,
          metric: 'cosine',
          spec: {
            serverless: {
              cloud: 'aws',
              region: 'us-east-1',
            },
          },
        });
        console.log(`✅ Index "${indexName}" created successfully`);
        console.log(`⏳ Note: It may take a few minutes for the index to be fully ready`);
      } catch (error) {
        console.error(`❌ Failed to create index:`, error instanceof Error ? error.message : String(error));
      }
    }

    console.log('\n🎉 Pinecone connection test completed successfully!');

  } catch (error) {
    console.error('\n❌ Pinecone connection test failed:');
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
    
    // Provide troubleshooting tips
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Verify your Pinecone API key is correct');
    console.log('2. Check if your Pinecone account is active');
    console.log('3. Ensure you have internet connectivity');
    console.log('4. Try accessing Pinecone dashboard: https://app.pinecone.io/');
    console.log('5. Check Pinecone status: https://status.pinecone.io/');
    
    process.exit(1);
  }
}

// Run the test
testPineconeConnection().catch(console.error);
