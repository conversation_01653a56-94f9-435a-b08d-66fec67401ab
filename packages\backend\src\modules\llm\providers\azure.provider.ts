import { Injectable, Logger } from '@nestjs/common';
import ModelClient from '@azure-rest/ai-inference';
import { AzureKeyCredential } from '@azure/core-auth';
import { ChatMessage, Tool } from '@otrs-ai-powered/shared';
import { <PERSON>lmProvider, LlmProviderConfig, LlmCompletionResponse } from '../interfaces/llm-provider.interface';

/**
 * Azure AI-specific configuration interface extending base LLM config
 */
export interface AzureProviderConfig extends LlmProviderConfig {
  baseUrl: string; // Required for Azure AI endpoint
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Azure AI message format
 */
interface AzureMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tool_calls?: AzureToolCall[];
  tool_call_id?: string;
}

/**
 * Azure AI tool call format
 */
interface AzureToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

/**
 * Azure AI tool format
 */
interface AzureTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: any;
  };
}

/**
 * Azure AI chat completion request
 */
interface AzureChatCompletionRequest {
  model: string;
  messages: AzureMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  tools?: AzureTool[];
  tool_choice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
}

/**
 * Azure AI chat completion response
 */
interface AzureChatCompletionResponse {
  choices: Array<{
    message: {
      role: string;
      content: string;
      tool_calls?: AzureToolCall[];
    };
    finish_reason: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

@Injectable()
export class AzureProvider implements ILlmProvider {
  private readonly logger = new Logger(AzureProvider.name);
  private readonly client: ReturnType<typeof ModelClient>;
  private readonly config: AzureProviderConfig;

  constructor(config: LlmProviderConfig) {
    this.config = config as AzureProviderConfig;
    
    if (!this.config.baseUrl) {
      throw new Error('Azure AI baseUrl is required');
    }

    this.client = ModelClient(
      this.config.baseUrl,
      new AzureKeyCredential(this.config.apiKey)
    );

    this.logger.log(`Azure AI provider initialized with model: ${this.config.model}`);
  }

  async generateCompletion(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<LlmCompletionResponse> {
    try {
      this.logger.debug(`Generating completion with ${messages.length} messages`);

      // Validate input
      if (!messages || messages.length === 0) {
        throw new Error('Messages array cannot be empty');
      }

      // Convert messages to Azure format
      const azureMessages = this.convertMessagesToAzureFormat(messages);

      // Create Azure AI request
      const request: AzureChatCompletionRequest = {
        model: this.config.model,
        messages: azureMessages,
        temperature: this.config.temperature ?? 0.7,
        max_tokens: this.config.maxTokens ?? 2000,
      };

      // Add tools if provided
      if (tools && tools.length > 0) {
        request.tools = this.convertToolsToAzureFormat(tools);
        request.tool_choice = 'auto';
      }

      // Make request to Azure AI
      const response = await this.client.path('/chat/completions').post({
        body: request,
      });

      if (response.status !== '200') {
        throw new Error(`Azure AI API error: ${response.status} ${JSON.stringify(response.body)}`);
      }

      // Parse response
      return this.parseAzureResponse(response.body as AzureChatCompletionResponse);
    } catch (error) {
      this.logger.error('Error generating completion:', error);
      throw error;
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test with a simple message
      const testMessages: ChatMessage[] = [
        {
          id: 'test-1',
          role: 'user',
          content: 'Hello',
          timestamp: new Date().toISOString()
        }
      ];

      await this.generateCompletion(testMessages);
      return true;
    } catch (error) {
      this.logger.error('Config validation failed:', error);
      return false;
    }
  }

  getProviderName(): string {
    return 'Azure AI';
  }

  getModel(): string {
    return this.config.model;
  }

  /**
   * Convert ChatMessage[] to Azure AI message format
   */
  private convertMessagesToAzureFormat(messages: ChatMessage[]): AzureMessage[] {
    return messages.map(message => ({
      role: message.role,
      content: message.content,
    }));
  }

  /**
   * Convert Tool[] to Azure AI tool format
   */
  private convertToolsToAzureFormat(tools: Tool[]): AzureTool[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  /**
   * Parse Azure AI response to LlmCompletionResponse
   */
  private parseAzureResponse(response: AzureChatCompletionResponse): LlmCompletionResponse {
    const choice = response.choices[0];
    if (!choice) {
      throw new Error('No choices in Azure AI response');
    }

    const result: LlmCompletionResponse = {
      content: choice.message.content || '',
    };

    // Add usage information if available
    if (response.usage) {
      result.usage = {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      };
    }

    // Convert tool calls if present
    if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
      result.toolCalls = choice.message.tool_calls.map(toolCall => ({
        toolId: toolCall.id,
        toolName: toolCall.function.name,
        arguments: JSON.parse(toolCall.function.arguments),
      }));
    }

    return result;
  }
}
