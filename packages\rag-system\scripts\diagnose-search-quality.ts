#!/usr/bin/env ts-node

/**
 * Comprehensive Search Quality Diagnostic Tool
 * 
 * This script performs a thorough analysis of the RAG system's search quality,
 * including relevance testing, semantic discrimination, and performance benchmarks.
 * 
 * Usage: npx ts-node scripts/diagnose-search-quality.ts
 */

import { RagService, RagConfig } from '../src/retrieval/retriever.service';
import { logger } from '../src/utils/logger';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.test') });

interface TestCase {
  query: string;
  expectedKeywords: string[];
  category: string;
  minScore: number;
}

async function diagnoseSearchQuality() {
  try {
    logger.info('🔍 RAG System Search Quality Diagnostic');
    logger.info('=====================================');

    // Configuration
    const config: RagConfig = {
      embedding: {
        provider: 'vllm',
        apiKey: process.env.EMBEDDING_API_KEY || 'test-key',
        model: process.env.EMBEDDING_MODEL || 'sentence-transformers/all-MiniLM-L6-v2',
        baseUrl: process.env.EMBEDDING_BASE_URL,
        dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '384'),
        batchSize: 10,
        timeout: 30000,
      },
      vectorDb: {
        apiKey: process.env.PINECONE_API_KEY || '',
        indexName: process.env.PINECONE_INDEX_NAME || 'otrs-faq-sentence-transformers-384',
        dimension: parseInt(process.env.PINECONE_DIMENSION || '384'),
        metric: 'cosine',
      },
      retrieval: {
        topK: 10,
        similarityThreshold: 0.0, // No threshold for full analysis
        maxContextLength: 4000,
        enableReranking: true,
      },
    };

    const ragService = new RagService(config);
    await ragService.initialize();

    // Test cases for quality assessment
    const testCases: TestCase[] = [
      {
        query: 'How do I configure email settings?',
        expectedKeywords: ['outlook', 'email', 'sign in', 'access'],
        category: 'Outlook & Email Security',
        minScore: 0.3,
      },
      {
        query: 'Password reset procedure',
        expectedKeywords: ['password', 'reset', 'Microsoft 365'],
        category: 'Outlook & Email Security',
        minScore: 0.4,
      },
      {
        query: 'SharePoint file sharing',
        expectedKeywords: ['share', 'file', 'SharePoint', 'secure'],
        category: 'OneDrive & SharePoint File Management',
        minScore: 0.4,
      },
      {
        query: 'Teams meeting setup',
        expectedKeywords: ['Teams', 'meeting', 'join', 'record'],
        category: 'Teams & Collaboration',
        minScore: 0.5,
      },
      {
        query: 'MFA authentication issues',
        expectedKeywords: ['MFA', 'authentication', 'multi-factor'],
        category: 'MFA & Authentication',
        minScore: 0.5,
      },
      {
        query: 'Suspicious email security',
        expectedKeywords: ['suspicious', 'email', 'security', 'phishing'],
        category: 'Security & Compliance',
        minScore: 0.4,
      },
    ];

    logger.info('\n📊 Test Configuration:');
    logger.info(`   Model: ${config.embedding.model}`);
    logger.info(`   Dimensions: ${config.embedding.dimensions}`);
    logger.info(`   Index: ${config.vectorDb.indexName}`);
    logger.info(`   Test Cases: ${testCases.length}`);

    // Run quality tests
    let totalRelevantResults = 0;
    let perfectMatches = 0;
    let totalQueries = testCases.length;
    const performanceResults: Array<{
      query: string;
      responseTime: number;
      resultCount: number;
      topScore: number;
      isRelevant: boolean;
      isPerfect: boolean;
    }> = [];

    logger.info('\n🔍 Running Search Quality Tests...');

    for (const testCase of testCases) {
      logger.info(`\n📋 Testing: "${testCase.query}"`);
      logger.info(`   Expected Category: ${testCase.category}`);
      logger.info(`   Expected Keywords: ${testCase.expectedKeywords.join(', ')}`);
      logger.info(`   Minimum Score: ${testCase.minScore}`);

      const startTime = Date.now();
      const results = await ragService.searchFaqs(testCase.query, {
        topK: 5,
        similarityThreshold: 0.1, // Low threshold for analysis
      });
      const responseTime = Date.now() - startTime;

      logger.info(`   Results Found: ${results.results.length} in ${responseTime}ms`);

      let isRelevant = false;
      let isPerfect = false;

      if (results.results.length > 0) {
        const topResult = results.results[0];
        logger.info(`   Top Result (Score: ${topResult.score.toFixed(3)}):`);
        logger.info(`     Category: ${topResult.category}`);
        logger.info(`     Question: ${topResult.question}`);

        // Check relevance
        const isRelevantCategory = topResult.category === testCase.category;
        const hasRelevantKeywords = testCase.expectedKeywords.some(keyword =>
          topResult.question.toLowerCase().includes(keyword.toLowerCase()) ||
          topResult.answer.toLowerCase().includes(keyword.toLowerCase())
        );

        if (isRelevantCategory && hasRelevantKeywords && topResult.score >= testCase.minScore) {
          logger.info('     ✅ PERFECT MATCH - Correct category, keywords, and score');
          isPerfect = true;
          isRelevant = true;
          perfectMatches++;
          totalRelevantResults++;
        } else if (hasRelevantKeywords && topResult.score >= (testCase.minScore * 0.8)) {
          logger.info('     ✅ GOOD MATCH - Relevant keywords and decent score');
          isRelevant = true;
          totalRelevantResults++;
        } else {
          logger.info('     ⚠️ PARTIAL MATCH - Some relevance but could be better');
        }

        // Show score distribution
        logger.info('   Score Distribution:');
        results.results.slice(0, 3).forEach((result, index) => {
          logger.info(`     ${index + 1}. ${result.score.toFixed(3)} - ${result.question.substring(0, 60)}...`);
        });
      } else {
        logger.info('     ❌ NO RESULTS - Query needs optimization');
      }

      performanceResults.push({
        query: testCase.query,
        responseTime,
        resultCount: results.results.length,
        topScore: results.results[0]?.score || 0,
        isRelevant,
        isPerfect,
      });
    }

    // Calculate quality metrics
    const relevanceRate = (totalRelevantResults / totalQueries) * 100;
    const perfectMatchRate = (perfectMatches / totalQueries) * 100;
    const avgResponseTime = performanceResults.reduce((sum, r) => sum + r.responseTime, 0) / performanceResults.length;
    const avgTopScore = performanceResults.reduce((sum, r) => sum + r.topScore, 0) / performanceResults.length;

    logger.info('\n📊 SEARCH QUALITY SUMMARY');
    logger.info('========================');
    logger.info(`   Total Queries Tested: ${totalQueries}`);
    logger.info(`   Relevant Results: ${totalRelevantResults}/${totalQueries} (${relevanceRate.toFixed(1)}%)`);
    logger.info(`   Perfect Matches: ${perfectMatches}/${totalQueries} (${perfectMatchRate.toFixed(1)}%)`);
    logger.info(`   Average Response Time: ${avgResponseTime.toFixed(0)}ms`);
    logger.info(`   Average Top Score: ${avgTopScore.toFixed(3)}`);

    // Quality assessment
    logger.info('\n🎯 QUALITY ASSESSMENT');
    logger.info('====================');
    
    if (relevanceRate >= 80) {
      logger.info('   ✅ EXCELLENT - Search quality is production-ready');
    } else if (relevanceRate >= 60) {
      logger.info('   ✅ GOOD - Search quality is acceptable for production');
    } else if (relevanceRate >= 40) {
      logger.info('   ⚠️ FAIR - Search quality needs improvement');
    } else {
      logger.info('   ❌ POOR - Search quality requires significant work');
    }

    if (avgResponseTime <= 1000) {
      logger.info('   ✅ EXCELLENT - Response times are very fast');
    } else if (avgResponseTime <= 2000) {
      logger.info('   ✅ GOOD - Response times are acceptable');
    } else {
      logger.info('   ⚠️ SLOW - Response times may impact user experience');
    }

    // Recommendations
    logger.info('\n💡 RECOMMENDATIONS');
    logger.info('==================');
    
    if (relevanceRate < 80) {
      logger.info('   • Consider adjusting similarity threshold (current: 0.3)');
      logger.info('   • Review FAQ data quality and categorization');
      logger.info('   • Validate embedding model performance');
    }
    
    if (avgResponseTime > 2000) {
      logger.info('   • Optimize vector database performance');
      logger.info('   • Consider reducing topK for faster searches');
      logger.info('   • Monitor API response times');
    }
    
    if (perfectMatchRate < 50) {
      logger.info('   • Review and improve FAQ categorization');
      logger.info('   • Consider retraining or switching embedding models');
      logger.info('   • Validate search query preprocessing');
    }

    logger.info('\n🎉 Search Quality Diagnostic Complete!');

  } catch (error) {
    logger.error('❌ Search quality diagnostic failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  diagnoseSearchQuality();
}
