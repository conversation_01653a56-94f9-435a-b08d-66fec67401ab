FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./
COPY packages/shared/package.json ./packages/shared/
COPY packages/mcp-server/package.json ./packages/mcp-server/

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build shared package
RUN yarn workspace @otrs-ai-powered/shared build

# Build MCP server
RUN yarn workspace @otrs-ai-powered/mcp-server build

# Production image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./
COPY packages/shared/package.json ./packages/shared/
COPY packages/mcp-server/package.json ./packages/mcp-server/

# Install production dependencies
RUN yarn install --frozen-lockfile --production

# Copy built files
COPY --from=builder /app/packages/shared/dist ./packages/shared/dist
COPY --from=builder /app/packages/mcp-server/dist ./packages/mcp-server/dist

# Expose port
EXPOSE 5000

# Start the application
CMD ["node", "packages/mcp-server/dist/index.js"]
