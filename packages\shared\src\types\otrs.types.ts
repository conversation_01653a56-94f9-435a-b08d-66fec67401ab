/**
 * Represents an OTRS ticket
 */
export interface OtrsTicket {
  id: string;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  queue: string;
  customer: string;
  agent?: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  attachments: Attachment[];
  customFields?: Record<string, any>;
}

/**
 * Represents the status of a ticket
 */
export enum TicketStatus {
  NEW = 'new',
  OPEN = 'open',
  PENDING = 'pending',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

/**
 * Represents the priority of a ticket
 */
export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * Represents an attachment in a ticket
 */
export interface Attachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
}

/**
 * Represents a request to create a new ticket
 */
export interface CreateTicketRequest {
  title: string;
  description: string;
  priority: TicketPriority;
  queue: string;
  customer: string;
  tags?: string[];
  attachments?: Attachment[];
  customFields?: Record<string, any>;
}

/**
 * Represents a request to update an existing ticket
 */
export interface UpdateTicketRequest {
  id: string;
  title?: string;
  description?: string;
  status?: TicketStatus;
  priority?: TicketPriority;
  queue?: string;
  agent?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}
