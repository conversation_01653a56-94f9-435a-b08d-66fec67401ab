import * as XLSX from 'xlsx';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { FaqEntry, ProcessedDocument, DataProcessingResult } from '../types/document';
import { logger } from '../utils/logger';

export class DataProcessor {
  private readonly excelFilePath: string;

  constructor() {
    this.excelFilePath = path.join(__dirname, '..', '..', 'data', 'sample', 'OTRS_FAQ.xlsx');
  }

  /**
   * Process Excel file and convert to FAQ entries
   */
  async processExcelToFaqEntries(): Promise<DataProcessingResult> {
    try {
      logger.info('Starting Excel data processing...');

      // Read Excel file
      const workbook = XLSX.readFile(this.excelFilePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON with proper headers
      const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (rawData.length < 2) {
        throw new Error('Excel file must have at least a header row and one data row');
      }

      // Extract headers and data
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1);

      logger.info(`Processing ${dataRows.length} FAQ entries with columns: ${headers.join(', ')}`);

      // Process each row
      const result: DataProcessingResult = {
        totalProcessed: dataRows.length,
        successful: 0,
        failed: 0,
        errors: [],
        statistics: {
          averageQuestionLength: 0,
          averageAnswerLength: 0,
          categoriesCount: 0,
          categories: [],
        },
      };

      const faqEntries: FaqEntry[] = [];
      const questionLengths: number[] = [];
      const answerLengths: number[] = [];
      const categories = new Set<string>();

      for (let i = 0; i < dataRows.length; i++) {
        try {
          const row = dataRows[i] as any[];
          const faqEntry = this.processRow(row, headers, i);
          
          if (faqEntry) {
            faqEntries.push(faqEntry);
            result.successful++;

            // Collect statistics
            questionLengths.push(faqEntry.question.length);
            answerLengths.push(faqEntry.answer.length);
            categories.add(faqEntry.category);
          }
        } catch (error) {
          result.failed++;
          result.errors.push({
            index: i,
            error: error instanceof Error ? error.message : 'Unknown error',
            data: dataRows[i],
          });
          logger.warn(`Failed to process row ${i + 1}:`, error);
        }
      }

      // Calculate statistics
      result.statistics = {
        averageQuestionLength: questionLengths.length > 0 
          ? Math.round(questionLengths.reduce((sum, len) => sum + len, 0) / questionLengths.length)
          : 0,
        averageAnswerLength: answerLengths.length > 0 
          ? Math.round(answerLengths.reduce((sum, len) => sum + len, 0) / answerLengths.length)
          : 0,
        categoriesCount: categories.size,
        categories: Array.from(categories).sort(),
      };

      logger.info(`Processing complete: ${result.successful} successful, ${result.failed} failed`);
      logger.info(`Statistics: Avg Q length: ${result.statistics.averageQuestionLength}, Avg A length: ${result.statistics.averageAnswerLength}`);
      logger.info(`Categories (${result.statistics.categoriesCount}): ${result.statistics.categories.join(', ')}`);

      return result;

    } catch (error) {
      logger.error('Error processing Excel data:', error);
      throw error;
    }
  }

  /**
   * Process a single row into a FAQ entry
   */
  private processRow(row: any[], _headers: string[], index: number): FaqEntry | null {
    // Map columns - assuming first 3 columns are Category, Question, Answer
    const category = this.getColumnValue(row, 0, 'Category');
    const question = this.getColumnValue(row, 1, 'Question');
    const answer = this.getColumnValue(row, 2, 'Answer');

    // Validate required fields
    if (!category || !question || !answer) {
      throw new Error(`Missing required fields at row ${index + 1}: category=${!!category}, question=${!!question}, answer=${!!answer}`);
    }

    // Clean and validate data
    const cleanCategory = this.cleanText(category);
    const cleanQuestion = this.cleanText(question);
    const cleanAnswer = this.cleanText(answer);

    if (cleanQuestion.length < 10) {
      throw new Error(`Question too short (${cleanQuestion.length} chars): ${cleanQuestion}`);
    }

    if (cleanAnswer.length < 10) {
      throw new Error(`Answer too short (${cleanAnswer.length} chars): ${cleanAnswer}`);
    }

    return {
      id: uuidv4(),
      category: cleanCategory,
      question: cleanQuestion,
      answer: cleanAnswer,
      metadata: {
        source: 'OTRS_FAQ.xlsx',
        lastUpdated: new Date().toISOString(),
        language: 'en',
      },
    };
  }

  /**
   * Get column value with fallback
   */
  private getColumnValue(row: any[], index: number, _columnName: string): string | null {
    if (index >= row.length) {
      return null;
    }

    const value = row[index];
    if (value === undefined || value === null || value === '') {
      return null;
    }

    return String(value).trim();
  }

  /**
   * Clean and normalize text
   */
  private cleanText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[\r\n]+/g, ' ') // Replace line breaks with spaces
      .replace(/[""]/g, '"') // Normalize quotes
      .replace(/['']/g, "'"); // Normalize apostrophes
  }

  /**
   * Convert FAQ entries to processed documents for vectorization
   */
  async convertToProcessedDocuments(faqEntries: FaqEntry[]): Promise<ProcessedDocument[]> {
    logger.info(`Converting ${faqEntries.length} FAQ entries to processed documents...`);

    const processedDocs: ProcessedDocument[] = [];

    for (const faq of faqEntries) {
      // Combine question and answer for embedding
      const content = `Question: ${faq.question}\n\nAnswer: ${faq.answer}`;

      const processedDoc: ProcessedDocument = {
        id: faq.id,
        content,
        metadata: {
          type: 'faq',
          category: faq.category,
          title: faq.question,
          source: faq.metadata?.source || 'unknown',
          lastUpdated: faq.metadata?.lastUpdated || new Date().toISOString(),
          contentLength: content.length,
          tags: [faq.category.toLowerCase().replace(/\s+/g, '-')],
        },
      };

      processedDocs.push(processedDoc);
    }

    logger.info(`Successfully converted ${processedDocs.length} documents`);
    return processedDocs;
  }

  /**
   * Validate FAQ entry data quality
   */
  validateFaqEntry(faq: FaqEntry): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!faq.id || faq.id.length === 0) {
      errors.push('Missing or empty ID');
    }

    if (!faq.category || faq.category.length < 2) {
      errors.push('Missing or too short category');
    }

    if (!faq.question || faq.question.length < 10) {
      errors.push('Missing or too short question (minimum 10 characters)');
    }

    if (!faq.answer || faq.answer.length < 10) {
      errors.push('Missing or too short answer (minimum 10 characters)');
    }

    // Check for suspicious content
    if (faq.question && faq.question.toLowerCase().includes('test')) {
      errors.push('Question appears to be test data');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Convert Excel data to FAQ entries array
   */
  async convertToFaqEntries(): Promise<FaqEntry[]> {
    try {
      const faqEntries: FaqEntry[] = [];

      // Read Excel file again to get the actual data
      const workbook = XLSX.readFile(this.excelFilePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (rawData.length < 2) {
        return faqEntries;
      }

      const dataRows = rawData.slice(1);

      for (let i = 0; i < dataRows.length; i++) {
        try {
          const row = dataRows[i] as any[];
          const faqEntry = this.processRow(row, ['Category', 'Question', 'Answer'], i);

          if (faqEntry) {
            faqEntries.push(faqEntry);
          }
        } catch (error) {
          // Skip failed rows - log for debugging if needed
          logger.debug(`Skipping row ${i + 1}:`, error instanceof Error ? error.message : String(error));
        }
      }

      return faqEntries;
    } catch (error) {
      logger.error('Error converting to FAQ entries:', error);
      return [];
    }
  }

  /**
   * Get processing statistics
   */
  async getProcessingStatistics(): Promise<any> {
    const result = await this.processExcelToFaqEntries();
    return {
      totalEntries: result.totalProcessed,
      successfulEntries: result.successful,
      failedEntries: result.failed,
      errorRate: result.totalProcessed > 0 ? (result.failed / result.totalProcessed) * 100 : 0,
      statistics: result.statistics,
      errors: result.errors,
    };
  }
}

// Export singleton instance
export const dataProcessor = new DataProcessor();
