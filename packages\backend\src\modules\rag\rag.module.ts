import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RagService } from './rag.service';
import { RagController } from './rag.controller';
import { RagHttpClientService } from './rag-http-client.service';

@Module({
  imports: [ConfigModule],
  providers: [RagHttpClientService, RagService],
  controllers: [RagController],
  exports: [RagService],
})
export class RagModule {}
