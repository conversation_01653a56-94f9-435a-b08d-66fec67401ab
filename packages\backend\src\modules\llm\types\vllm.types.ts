import { LlmProviderConfig } from '../interfaces/llm-provider.interface';

/**
 * vLLM-specific configuration interface extending base LLM config
 */
export interface VllmProviderConfig extends LlmProviderConfig {
  baseUrl: string; // Required for vLLM
  retryAttempts?: number;
  retryDelay?: number;
  userAgent?: string;
}

/**
 * OpenAI-compatible request format for vLLM
 */
export interface VllmChatCompletionRequest {
  model: string;
  messages: VllmMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  stream?: boolean;
  tools?: VllmTool[];
  tool_choice?: VllmToolChoice;
  user?: string;
  logit_bias?: Record<string, number>;
  seed?: number;
}

/**
 * OpenAI-compatible message format
 */
export interface VllmMessage {
  role: VllmMessageRole;
  content: string | VllmMessageContent[];
  name?: string;
  tool_calls?: VllmToolCall[];
  tool_call_id?: string;
}

/**
 * Message roles supported by vLLM
 */
export type VllmMessageRole = 'system' | 'user' | 'assistant' | 'tool';

/**
 * Content types for multimodal messages
 */
export interface VllmMessageContent {
  type: 'text' | 'image_url';
  text?: string;
  image_url?: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
}

/**
 * OpenAI-compatible tool format
 */
export interface VllmTool {
  type: 'function';
  function: VllmFunction;
}

/**
 * Function definition for tools
 */
export interface VllmFunction {
  name: string;
  description?: string;
  parameters: VllmFunctionParameters;
}

/**
 * Function parameters schema (JSON Schema format)
 */
export interface VllmFunctionParameters {
  type: 'object';
  properties: Record<string, VllmParameterProperty>;
  required?: string[];
  additionalProperties?: boolean;
}

/**
 * Parameter property definition
 */
export interface VllmParameterProperty {
  type: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
  description?: string;
  enum?: any[];
  items?: VllmParameterProperty;
  properties?: Record<string, VllmParameterProperty>;
  default?: any;
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
}

/**
 * Tool choice options
 */
export type VllmToolChoice = 'none' | 'auto' | VllmToolChoiceFunction;

/**
 * Specific tool choice
 */
export interface VllmToolChoiceFunction {
  type: 'function';
  function: {
    name: string;
  };
}

/**
 * OpenAI-compatible tool call format
 */
export interface VllmToolCall {
  id: string;
  type: 'function';
  function: VllmToolCallFunction;
}

/**
 * Tool call function details
 */
export interface VllmToolCallFunction {
  name: string;
  arguments: string; // JSON string
}

/**
 * vLLM response format
 */
export interface VllmChatCompletionResponse {
  id: string;
  object: 'chat.completion' | 'chat.completion.chunk';
  created: number;
  model: string;
  choices: VllmChoice[];
  usage?: VllmUsage;
  system_fingerprint?: string;
}

/**
 * Response choice
 */
export interface VllmChoice {
  index: number;
  message?: VllmMessage;
  delta?: VllmMessageDelta;
  finish_reason: VllmFinishReason;
  logprobs?: VllmLogprobs;
}

/**
 * Message delta for streaming responses
 */
export interface VllmMessageDelta {
  role?: VllmMessageRole;
  content?: string;
  tool_calls?: VllmToolCallDelta[];
}

/**
 * Tool call delta for streaming
 */
export interface VllmToolCallDelta {
  index: number;
  id?: string;
  type?: 'function';
  function?: {
    name?: string;
    arguments?: string;
  };
}

/**
 * Finish reasons
 */
export type VllmFinishReason = 'stop' | 'length' | 'tool_calls' | 'content_filter' | 'function_call';

/**
 * Token usage information
 */
export interface VllmUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

/**
 * Log probabilities (if requested)
 */
export interface VllmLogprobs {
  tokens: string[];
  token_logprobs: number[];
  top_logprobs?: Record<string, number>[];
  text_offset?: number[];
}

/**
 * Phi-4 specific chat format templates
 */
export interface Phi4ChatFormat {
  systemTemplate: string;
  userTemplate: string;
  assistantTemplate: string;
  toolTemplate: string;
  endToken: string;
}

/**
 * Default Phi-4 chat format
 */
export const PHI4_CHAT_FORMAT: Phi4ChatFormat = {
  systemTemplate: '<|system|>{content}<|end|>',
  userTemplate: '<|user|>{content}<|end|>',
  assistantTemplate: '<|assistant|>{content}<|end|>',
  toolTemplate: '<|tool|>{content}<|/tool|>',
  endToken: '<|end|>',
};

/**
 * Chat format conversion options
 */
export interface ChatFormatOptions {
  includeSystemMessage: boolean;
  systemMessageContent?: string;
  toolFormat: 'mcp' | 'openai';
  addAssistantPrompt: boolean;
}

/**
 * Default chat format options
 */
export const DEFAULT_CHAT_FORMAT_OPTIONS: ChatFormatOptions = {
  includeSystemMessage: true,
  systemMessageContent: 'You are a helpful assistant.',
  toolFormat: 'mcp',
  addAssistantPrompt: true,
};

/**
 * vLLM provider metrics
 */
export interface VllmProviderMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  averageTokensPerRequest: number;
  errorsByType: Record<string, number>;
  lastRequestTime?: string;
  lastErrorTime?: string;
}

/**
 * vLLM provider health status
 */
export interface VllmProviderHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: string;
  responseTime?: number;
  errorRate: number;
  uptime: number;
  version?: string;
}

/**
 * vLLM model information
 */
export interface VllmModelInfo {
  id: string;
  object: 'model';
  created: number;
  owned_by: string;
  permission?: VllmModelPermission[];
  root?: string;
  parent?: string;
}

/**
 * Model permissions
 */
export interface VllmModelPermission {
  id: string;
  object: 'model_permission';
  created: number;
  allow_create_engine: boolean;
  allow_sampling: boolean;
  allow_logprobs: boolean;
  allow_search_indices: boolean;
  allow_view: boolean;
  allow_fine_tuning: boolean;
  organization: string;
  group?: string;
  is_blocking: boolean;
}

/**
 * vLLM server information
 */
export interface VllmServerInfo {
  version: string;
  model_name: string;
  model_path?: string;
  block_size: number;
  max_model_len: number;
  max_num_batched_tokens: number;
  max_num_seqs: number;
  max_paddings: number;
  gpu_memory_utilization: number;
  tensor_parallel_size: number;
  quantization?: string;
  served_model_name?: string;
}
