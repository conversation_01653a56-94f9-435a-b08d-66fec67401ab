/**
 * Represents a user in the system
 */
export interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  metadata?: Record<string, any>;
}

/**
 * Represents authentication credentials
 */
export interface AuthCredentials {
  username: string;
  password: string;
}

/**
 * Represents a JWT token response
 */
export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

/**
 * Represents a refresh token request
 */
export interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * Represents the payload of a JWT token
 */
export interface JwtPayload {
  sub: string;
  username: string;
  roles: string[];
  permissions: string[];
  iat: number;
  exp: number;
}
