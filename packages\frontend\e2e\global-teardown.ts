/**
 * Playwright Global Teardown
 * 
 * Performs cleanup tasks after running E2E tests, including:
 * - Test data cleanup
 * - Resource cleanup
 * - Report generation
 */

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test global teardown...');

  const skipExternalServices = process.env.SKIP_EXTERNAL_SERVICES === 'true';

  if (skipExternalServices) {
    console.log('⚠️  External services disabled - minimal teardown');
    return;
  }

  try {
    // Cleanup test data if needed
    console.log('🗑️  Cleaning up test data...');
    // Add any test data cleanup here

    // Generate test summary
    console.log('📊 Generating test summary...');
    // Add any summary generation here

    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown;
