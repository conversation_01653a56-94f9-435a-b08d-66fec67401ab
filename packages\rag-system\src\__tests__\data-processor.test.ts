import { dataProcessor } from '../indexing/data-processor';

describe('DataProcessor', () => {

  describe('Excel Processing', () => {
    test('should process Excel file and extract FAQ entries', async () => {
      const result = await dataProcessor.processExcelToFaqEntries();
      
      expect(result).toHaveProperty('totalProcessed');
      expect(result).toHaveProperty('successful');
      expect(result).toHaveProperty('failed');
      expect(result).toHaveProperty('statistics');
      expect(result).toHaveProperty('errors');
      
      expect(result.totalProcessed).toBeGreaterThan(0);
      expect(result.successful).toBeGreaterThan(0);
      expect(result.successful).toBeLessThanOrEqual(result.totalProcessed);
      
      // Should have reasonable success rate
      const successRate = (result.successful / result.totalProcessed) * 100;
      expect(successRate).toBeGreaterThan(80); // At least 80% success rate
    }, 30000);

    test('should convert to FAQ entries array', async () => {
      const faqEntries = await dataProcessor.convertToFaqEntries();
      
      expect(Array.isArray(faqEntries)).toBe(true);
      expect(faqEntries.length).toBeGreaterThan(0);
      
      // Check structure of FAQ entries
      faqEntries.forEach(entry => {
        expect(entry).toHaveProperty('id');
        expect(entry).toHaveProperty('question');
        expect(entry).toHaveProperty('answer');
        expect(entry).toHaveProperty('category');
        expect(entry).toHaveProperty('metadata');
        
        expect(typeof entry.question).toBe('string');
        expect(typeof entry.answer).toBe('string');
        expect(typeof entry.category).toBe('string');
        expect(entry.question.length).toBeGreaterThan(0);
        expect(entry.answer.length).toBeGreaterThan(0);
      });
    }, 30000);

    test('should convert to processed documents', async () => {
      const faqEntries = await dataProcessor.convertToFaqEntries();
      const processedDocs = await dataProcessor.convertToProcessedDocuments(faqEntries);
      
      expect(Array.isArray(processedDocs)).toBe(true);
      expect(processedDocs.length).toBe(faqEntries.length);
      
      processedDocs.forEach((doc, index) => {
        expect(doc).toHaveProperty('id');
        expect(doc).toHaveProperty('content');
        expect(doc).toHaveProperty('metadata');
        
        expect(typeof doc.content).toBe('string');
        expect(doc.content.length).toBeGreaterThan(0);
        expect(doc.metadata).toHaveProperty('title');
        expect(doc.metadata).toHaveProperty('category');
        expect(doc.metadata).toHaveProperty('type');
        
        // Content should include both question and answer
        expect(doc.content).toContain(faqEntries[index].question);
        expect(doc.content).toContain(faqEntries[index].answer);
      });
    }, 30000);

    test('should provide processing statistics', async () => {
      const stats = await dataProcessor.getProcessingStatistics();
      
      expect(stats).toHaveProperty('totalEntries');
      expect(stats).toHaveProperty('successfulEntries');
      expect(stats).toHaveProperty('failedEntries');
      expect(stats).toHaveProperty('errorRate');
      expect(stats).toHaveProperty('statistics');
      
      expect(stats.totalEntries).toBeGreaterThan(0);
      expect(stats.successfulEntries).toBeGreaterThan(0);
      expect(stats.errorRate).toBeGreaterThanOrEqual(0);
      expect(stats.errorRate).toBeLessThan(50); // Less than 50% error rate
      
      expect(stats.statistics).toHaveProperty('categories');
      expect(Array.isArray(stats.statistics.categories)).toBe(true);
      expect(stats.statistics.categories.length).toBeGreaterThan(0);
    }, 30000);
  });

  describe('Data Validation', () => {
    test('should identify FAQ categories correctly', async () => {
      const stats = await dataProcessor.getProcessingStatistics();
      const categories = stats.statistics.categories;
      
      // Should have expected OTRS categories
      const expectedCategories = [
        'MFA & Authentication',
        'OneDrive & SharePoint File Management',
        'Outlook & Email Security',
        'Security & Compliance',
        'Teams & Collaboration'
      ];
      
      expectedCategories.forEach(expectedCategory => {
        expect(categories).toContain(expectedCategory);
      });
    }, 30000);

    test('should have reasonable content lengths', async () => {
      const faqEntries = await dataProcessor.convertToFaqEntries();
      
      const questionLengths = faqEntries.map(entry => entry.question.length);
      const answerLengths = faqEntries.map(entry => entry.answer.length);
      
      const avgQuestionLength = questionLengths.reduce((sum, len) => sum + len, 0) / questionLengths.length;
      const avgAnswerLength = answerLengths.reduce((sum, len) => sum + len, 0) / answerLengths.length;
      
      // Reasonable content lengths
      expect(avgQuestionLength).toBeGreaterThan(20);
      expect(avgQuestionLength).toBeLessThan(200);
      expect(avgAnswerLength).toBeGreaterThan(50);
      expect(avgAnswerLength).toBeLessThan(500);
      
      // No empty content
      questionLengths.forEach(len => expect(len).toBeGreaterThan(0));
      answerLengths.forEach(len => expect(len).toBeGreaterThan(0));
    }, 30000);
  });

  describe('Error Handling', () => {
    test('should handle missing Excel file gracefully', async () => {
      // This test would require mocking or using a non-existent file
      // For now, we'll test that the current file processing doesn't throw
      await expect(dataProcessor.processExcelToFaqEntries()).resolves.toBeDefined();
    });

    test('should handle malformed data gracefully', async () => {
      const result = await dataProcessor.processExcelToFaqEntries();
      
      // Should have some failed entries due to header row or malformed data
      expect(result.failed).toBeGreaterThanOrEqual(0);
      
      // But should still process most entries successfully
      const successRate = (result.successful / result.totalProcessed) * 100;
      expect(successRate).toBeGreaterThan(50);
    }, 30000);
  });
});
