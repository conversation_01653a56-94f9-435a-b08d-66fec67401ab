import { UpdateTicketRequest, TicketStatus, TicketPriority } from '@otrs-ai-powered/shared';
import { otrsApiService } from '../../services/otrs-api.service';
import { validateRequiredFields, validateStringLength, validateEnum } from '../../utils/validation';
import { withErrorHandling, ValidationError } from '../../utils/error-handling';
import { logger } from '../../utils/logger';

interface UpdateTicketArgs {
  ticketId: string;
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  queue?: string;
  agent?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

/**
 * Tool to update an existing ticket in OTRS
 */
export const updateTicket = withErrorHandling(async (args: UpdateTicketArgs) => {
  logger.info('Updating ticket with args:', args);
  
  // Validate required fields
  validateRequiredFields(args, ['ticketId']);
  
  // Validate string lengths if provided
  if (args.title) {
    validateStringLength(args.title, 'title', 5, 100);
  }
  
  if (args.description) {
    validateStringLength(args.description, 'description', 10, 5000);
  }
  
  // Validate status if provided
  if (args.status) {
    try {
      validateEnum(
        args.status.toLowerCase() as TicketStatus,
        'status',
        Object.values(TicketStatus)
      );
    } catch (error) {
      throw new ValidationError(
        `Invalid status. Must be one of: ${Object.values(TicketStatus).join(', ')}`
      );
    }
  }
  
  // Validate priority if provided
  if (args.priority) {
    try {
      validateEnum(
        args.priority.toLowerCase() as TicketPriority,
        'priority',
        Object.values(TicketPriority)
      );
    } catch (error) {
      throw new ValidationError(
        `Invalid priority. Must be one of: ${Object.values(TicketPriority).join(', ')}`
      );
    }
  }
  
  // Prepare ticket data
  const ticketData: UpdateTicketRequest = {
    id: args.ticketId,
    title: args.title,
    description: args.description,
    status: args.status?.toLowerCase() as TicketStatus,
    priority: args.priority?.toLowerCase() as TicketPriority,
    queue: args.queue,
    agent: args.agent,
    tags: args.tags,
    customFields: args.customFields,
  };
  
  // Update ticket in OTRS
  const ticket = await otrsApiService.updateTicket(ticketData);
  
  return {
    success: true,
    ticketId: ticket.id,
    message: `Ticket ${ticket.id} updated successfully`,
    ticket,
  };
});
