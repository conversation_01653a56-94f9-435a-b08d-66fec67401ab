import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { RagService, RagConfig } from '../retrieval/retriever.service';
import { dataProcessor } from '../indexing/data-processor';
import { logger } from '../utils/logger';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Mock fetch for Azure AI API
global.fetch = jest.fn();

// Load test environment variables
dotenv.config({ path: path.join(__dirname, '../../.env.test') });

describe('RAG System Integration Tests', () => {
  let ragService: RagService;
  let testConfig: RagConfig;

  beforeAll(async () => {
    // Skip tests if no real API key is provided
    if (process.env.TEST_SKIP_REAL_API === 'true') {
      console.log('Skipping RAG integration tests - TEST_SKIP_REAL_API is true');
      return;
    }

    testConfig = {
      embedding: {
        provider: (process.env.EMBEDDING_PROVIDER as 'openai' | 'vllm' | 'azure') || 'azure',
        apiKey: process.env.EMBEDDING_API_KEY || 'test-key',
        model: process.env.EMBEDDING_MODEL || 'text-embedding-3-small',
        baseUrl: process.env.EMBEDDING_BASE_URL || 'https://embed-mini-all-l6v2-resource.openai.azure.com',
        dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '384'),
        batchSize: parseInt(process.env.EMBEDDING_BATCH_SIZE || '10'),
        timeout: 30000,
      },
      vectorDb: {
        apiKey: process.env.PINECONE_API_KEY || '',
        indexName: process.env.PINECONE_INDEX_NAME || 'otrs-faq-sentence-transformers-384',
        dimension: parseInt(process.env.PINECONE_DIMENSION || '384'),
        metric: 'cosine' as const,
      },
      retrieval: {
        topK: parseInt(process.env.RAG_TOP_K || '5'),
        similarityThreshold: parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.3'),
        maxContextLength: parseInt(process.env.RAG_MAX_CONTEXT_LENGTH || '4000'),
        enableReranking: process.env.RAG_ENABLE_RERANKING === 'true',
      },
    };

    // Setup Azure AI API mock
    (fetch as jest.MockedFunction<typeof fetch>).mockImplementation(async (url, options) => {
      const body = JSON.parse(options?.body as string || '{}');
      const input = body.input;
      const isArray = Array.isArray(input);
      const inputs = isArray ? input : [input];

      // Create mock embeddings with semantic similarity
      const mockEmbeddings = inputs.map((text: string, index: number) => {
        const embedding = [];

        // Create deterministic embeddings based on text content for semantic testing
        const textLower = text.toLowerCase();
        const isEmailRelated = textLower.includes('email') || textLower.includes('configuration') || textLower.includes('setup');

        for (let j = 0; j < 384; j++) {
          if (isEmailRelated) {
            // Email-related texts get similar patterns
            embedding.push(0.5 + Math.sin(j * 0.1) * 0.3);
          } else {
            // Unrelated texts get different patterns
            embedding.push(0.2 + Math.cos(j * 0.2) * 0.2);
          }
        }

        return {
          object: 'embedding',
          embedding,
          index,
        };
      });

      const mockResponse = {
        data: mockEmbeddings,
        model: 'text-embedding-3-small',
        object: 'list',
        usage: {
          prompt_tokens: inputs.length * 10,
          total_tokens: inputs.length * 10,
        },
      };

      return {
        ok: true,
        json: async () => mockResponse,
      } as Response;
    });

    ragService = new RagService(testConfig);
  }, 30000);

  afterAll(async () => {
    // Cleanup test data if needed
    if (ragService && process.env.TEST_SKIP_REAL_API !== 'true') {
      try {
        // Note: Be careful with deleteAllVectors in production
        // await ragService.vectorDbService.deleteAllVectors();
        logger.info('Test cleanup completed');
      } catch (error) {
        logger.warn('Test cleanup failed:', error);
      }
    }
  });

  describe('Data Processing Pipeline', () => {
    test('should process Excel FAQ data successfully', async () => {
      const result = await dataProcessor.processExcelToFaqEntries();
      
      expect(result.totalProcessed).toBeGreaterThan(0);
      expect(result.successful).toBeGreaterThan(0);
      expect(result.statistics.categoriesCount).toBeGreaterThan(0);
      expect(result.statistics.categories).toContain('Outlook & Email Security');
      
      logger.info(`Processed ${result.successful}/${result.totalProcessed} FAQ entries`);
    });

    test('should validate FAQ entry data quality', async () => {
      const result = await dataProcessor.processExcelToFaqEntries();
      expect(result.statistics.averageQuestionLength).toBeGreaterThan(10);
      expect(result.statistics.averageAnswerLength).toBeGreaterThan(10);
      expect(result.failed).toBeLessThan(result.totalProcessed * 0.1); // Less than 10% failure rate
    });
  });

  describe('RAG Service Initialization', () => {
    test('should initialize RAG service successfully', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      await expect(ragService.initialize()).resolves.not.toThrow();
      
      const isAvailable = await ragService.isAvailable();
      expect(isAvailable).toBe(true);
    }, 60000);

    test('should get service statistics', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const stats = await ragService.getStatistics();
      expect(stats).toHaveProperty('isInitialized');
      expect(stats).toHaveProperty('vectorDatabase');
      expect(stats).toHaveProperty('embedding');
      expect(stats).toHaveProperty('config');
    });
  });

  describe('FAQ Data Indexing', () => {
    test('should index FAQ data to vector database', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const indexingResult = await ragService.indexFaqData();
      
      expect(indexingResult.totalDocuments).toBeGreaterThan(0);
      expect(indexingResult.successfullyIndexed).toBeGreaterThan(0);
      expect(indexingResult.statistics.totalTokensUsed).toBeGreaterThan(0);
      expect(indexingResult.statistics.categories.length).toBeGreaterThan(0);
      
      logger.info(`Indexed ${indexingResult.successfullyIndexed} documents in ${indexingResult.indexingTime}ms`);
    }, 120000);
  });

  describe('FAQ Search and Retrieval', () => {
    test('should search FAQs with semantic similarity', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const searchResult = await ragService.searchFaqs('How do I configure email settings?', {
        topK: 3,
        similarityThreshold: 0.1, // Lower threshold for better recall
      });

      expect(searchResult.results.length).toBeGreaterThan(0);
      expect(searchResult.results.length).toBeLessThanOrEqual(3);
      expect(searchResult.searchTime).toBeGreaterThan(0);
      expect(searchResult.embedding.dimensions).toBe(384);
      
      // Check result structure
      const firstResult = searchResult.results[0];
      expect(firstResult).toHaveProperty('id');
      expect(firstResult).toHaveProperty('score');
      expect(firstResult).toHaveProperty('question');
      expect(firstResult).toHaveProperty('answer');
      expect(firstResult).toHaveProperty('category');
      expect(firstResult.score).toBeGreaterThanOrEqual(0.3); // Adjusted for sentence-transformers model
      
      logger.info(`Found ${searchResult.results.length} relevant FAQs for email configuration query`);
    }, 30000);

    test('should filter FAQs by category', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const searchResult = await ragService.searchFaqs('security settings', {
        topK: 5,
        similarityThreshold: 0.1, // Lower threshold for better recall
        category: 'Security & Compliance',
      });

      expect(searchResult.results.length).toBeGreaterThan(0);
      searchResult.results.forEach(result => {
        expect(result.category).toBe('Security & Compliance');
      });
    }, 30000);

    test('should perform hybrid search with metadata filtering', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const searchResult = await ragService.hybridSearch(
        'email configuration',
        { category: 'Outlook & Email Security' },
        3
      );

      expect(searchResult.results.length).toBeGreaterThan(0);
      expect(searchResult.results.length).toBeLessThanOrEqual(3);
      
      // All results should be from the specified category
      searchResult.results.forEach(result => {
        expect(result.category).toContain('Outlook');
      });
    }, 30000);

    test('should return empty results for irrelevant queries', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const searchResult = await ragService.searchFaqs('quantum physics and molecular biology', {
        topK: 5,
        similarityThreshold: 0.8, // High threshold
      });

      // Should return few or no results for completely irrelevant query
      expect(searchResult.results.length).toBeLessThanOrEqual(2);
    }, 30000);
  });

  describe('Performance and Quality Metrics', () => {
    test('should meet performance benchmarks', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const queries = [
        'How to reset password?',
        'Email not working',
        'SharePoint access issues',
        'Teams collaboration setup',
        'MFA authentication problems',
      ];

      const results = [];
      
      for (const query of queries) {
        const startTime = Date.now();
        const searchResult = await ragService.searchFaqs(query, { topK: 3 });
        const responseTime = Date.now() - startTime;
        
        results.push({
          query,
          responseTime,
          resultCount: searchResult.results.length,
          topScore: searchResult.results[0]?.score || 0,
        });
      }

      // Performance assertions
      const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
      expect(avgResponseTime).toBeLessThan(5000); // Average response time under 5 seconds
      
      // Quality assertions - adjust for sentence-transformers model
      const resultsWithGoodScore = results.filter(r => r.topScore > 0.3); // Lower threshold for sentence-transformers
      expect(resultsWithGoodScore.length).toBeGreaterThanOrEqual(queries.length * 0.2); // 20% should have good relevance for sentence-transformers
      
      logger.info(`Performance test: ${avgResponseTime.toFixed(0)}ms average response time`);
      logger.info(`Quality test: ${resultsWithGoodScore.length}/${queries.length} queries with good relevance`);
    }, 60000);
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle empty queries gracefully', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      await expect(ragService.searchFaqs('')).rejects.toThrow();
    });

    test('should handle very long queries', async () => {
      if (process.env.TEST_SKIP_REAL_API === 'true') return;

      const longQuery = 'email configuration '.repeat(100);
      const searchResult = await ragService.searchFaqs(longQuery, { topK: 2 });
      
      expect(searchResult.results).toBeDefined();
      expect(searchResult.results.length).toBeLessThanOrEqual(2);
    }, 30000);

    test('should get available categories', async () => {
      const categories = await ragService.getCategories();
      
      expect(categories).toBeInstanceOf(Array);
      expect(categories.length).toBeGreaterThan(0);
      expect(categories).toContain('Outlook & Email Security');
    });
  });
});
