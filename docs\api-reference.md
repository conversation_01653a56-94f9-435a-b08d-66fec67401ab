# API Reference

This document provides a comprehensive reference for all API endpoints in the OTRS AI-Powered Chat Assistant.

## Base URLs

- **Backend API**: `https://api.example.com/api`
- **MCP Server**: `https://mcp.example.com/api`
- **RAG System**: `https://rag.example.com/api`

## Authentication

Most API endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

## Backend API

### Authentication

#### Login

```
POST /auth/login
```

Request body:
```json
{
  "username": "string",
  "password": "string"
}
```

Response:
```json
{
  "accessToken": "string",
  "refreshToken": "string",
  "expiresIn": 3600,
  "tokenType": "Bearer"
}
```

#### Logout

```
POST /auth/logout
```

Request body:
```json
{
  "refreshToken": "string"
}
```

Response:
```json
{
  "message": "Logged out successfully"
}
```

#### Refresh Token

```
POST /auth/refresh-token
```

Request body:
```json
{
  "refreshToken": "string"
}
```

Response:
```json
{
  "accessToken": "string",
  "refreshToken": "string",
  "expiresIn": 3600,
  "tokenType": "Bearer"
}
```

#### Get Current User

```
GET /auth/me
```

Response:
```json
{
  "id": "string",
  "username": "string",
  "email": "string",
  "roles": ["string"],
  "permissions": ["string"]
}
```

### Chat

#### Create Session

```
POST /chat/sessions
```

Request body:
```json
{
  "metadata": {
    "key": "value"
  }
}
```

Response:
```json
{
  "id": "string",
  "userId": "string",
  "messages": [
    {
      "id": "string",
      "content": "string",
      "role": "system",
      "timestamp": "string"
    }
  ],
  "createdAt": "string",
  "updatedAt": "string",
  "metadata": {
    "key": "value"
  }
}
```

#### Get Session

```
GET /chat/sessions/{sessionId}
```

Response:
```json
{
  "id": "string",
  "userId": "string",
  "messages": [
    {
      "id": "string",
      "content": "string",
      "role": "string",
      "timestamp": "string",
      "metadata": {
        "key": "value"
      }
    }
  ],
  "createdAt": "string",
  "updatedAt": "string",
  "metadata": {
    "key": "value"
  }
}
```

#### Get Chat History

```
GET /chat/history
```

Response:
```json
[
  {
    "id": "string",
    "userId": "string",
    "messages": [],
    "createdAt": "string",
    "updatedAt": "string",
    "metadata": {
      "key": "value"
    }
  }
]
```

#### Delete Session

```
DELETE /chat/sessions/{sessionId}
```

Response:
```json
{
  "message": "Session deleted successfully"
}
```

#### Send Message

```
POST /chat/messages
```

Request body:
```json
{
  "sessionId": "string",
  "content": "string",
  "metadata": {
    "key": "value"
  }
}
```

Response:
```json
{
  "id": "string",
  "content": "string",
  "role": "assistant",
  "timestamp": "string",
  "metadata": {
    "key": "value"
  }
}
```

## MCP Server API

### Execute Tool

```
POST /execute-tool
```

Request body:
```json
{
  "toolName": "string",
  "arguments": {
    "key": "value"
  }
}
```

Response:
```json
{
  "success": true,
  "result": {
    "key": "value"
  }
}
```

### Health Check

```
GET /health
```

Response:
```json
{
  "status": "ok"
}
```

## RAG System API

### Query

```
POST /query
```

Request body:
```json
{
  "query": "string",
  "topK": 5
}
```

Response:
```json
{
  "results": [
    {
      "document": {
        "id": "string",
        "content": "string",
        "metadata": {
          "key": "value"
        }
      },
      "score": 0.95
    }
  ]
}
```

### Index

```
POST /index
```

Request body:
```json
{
  "documents": [
    {
      "id": "string",
      "content": "string",
      "metadata": {
        "key": "value"
      }
    }
  ]
}
```

Response:
```json
{
  "success": true,
  "count": 1
}
```

### Health Check

```
GET /health
```

Response:
```json
{
  "status": "ok"
}
```

## WebSocket Events

The backend provides WebSocket events for real-time communication.

### Connection

Connect to the WebSocket server:

```
ws://api.example.com/socket.io
```

Include authentication in the connection:

```javascript
const socket = io({
  auth: {
    token: "your-access-token"
  }
});
```

### Events

#### Message

Receive a new message:

```javascript
socket.on("message", (message) => {
  console.log(message);
});
```

Message format:
```json
{
  "id": "string",
  "content": "string",
  "role": "assistant",
  "timestamp": "string",
  "metadata": {
    "key": "value"
  }
}
```

#### Typing

Receive typing indicator:

```javascript
socket.on("typing", (data) => {
  console.log(data);
});
```

Data format:
```json
{
  "sessionId": "string",
  "userId": "string"
}
```

Send typing indicator:

```javascript
socket.emit("typing", {
  "sessionId": "string"
});
```
