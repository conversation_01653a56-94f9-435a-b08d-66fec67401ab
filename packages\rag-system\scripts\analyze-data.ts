#!/usr/bin/env ts-node

import { dataAnalyzer } from '../src/utils/data-analyzer';
import { logger } from '../src/utils/logger';

async function main() {
  try {
    logger.info('Starting OTRS FAQ data analysis...');
    
    // Analyze the data structure
    const structure = await dataAnalyzer.analyzeFaqData();
    
    // Convert to FAQ entries for further inspection
    const faqEntries = await dataAnalyzer.convertToFaqEntries();
    
    logger.info('\n=== Analysis Complete ===');
    logger.info(`Successfully analyzed ${structure.totalRows} FAQ entries`);
    logger.info(`Data structure saved for processing pipeline design`);
    
    // Save analysis results to file for reference
    const fs = require('fs');
    const path = require('path');
    
    const analysisResults = {
      timestamp: new Date().toISOString(),
      structure,
      sampleEntries: faqEntries.slice(0, 5),
    };
    
    const outputPath = path.join(__dirname, '..', 'analysis-results.json');
    fs.writeFileSync(outputPath, JSON.stringify(analysisResults, null, 2));
    
    logger.info(`Analysis results saved to: ${outputPath}`);
    
  } catch (error) {
    logger.error('Analysis failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
