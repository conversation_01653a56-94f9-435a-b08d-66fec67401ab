import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LlmService } from './llm.service';
import { LlmProviderFactory } from './providers/llm-provider.factory';
import llmConfig from '../../config/llm.config';

@Module({
  imports: [
    ConfigModule.forFeature(llmConfig),
  ],
  providers: [
    LlmService,
    LlmProviderFactory,
  ],
  exports: [LlmService],
})
export class LlmModule {}
