/**
 * Interface for RAG (Retrieval-Augmented Generation) service
 */
export interface IRagService {
  /**
   * Initialize the RAG service with FAQ data
   * @param faqData Array of FAQ entries to be vectorized and stored
   */
  initializeWithFaqData(faqData: FaqEntry[]): Promise<void>;

  /**
   * Search for relevant FAQ entries based on user query
   * @param query User's question or query
   * @param topK Number of top results to return
   * @returns Promise resolving to relevant FAQ entries with similarity scores
   */
  searchRelevantFaqs(query: string, topK?: number): Promise<RelevantFaqResult[]>;

  /**
   * Add new FAQ entries to the vector database
   * @param faqEntries New FAQ entries to add
   */
  addFaqEntries(faqEntries: FaqEntry[]): Promise<void>;

  /**
   * Update existing FAQ entries in the vector database
   * @param faqEntries FAQ entries to update
   */
  updateFaqEntries(faqEntries: FaqEntry[]): Promise<void>;

  /**
   * Delete FAQ entries from the vector database
   * @param faqIds Array of FAQ IDs to delete
   */
  deleteFaqEntries(faqIds: string[]): Promise<void>;

  /**
   * Check if the RAG service is available and ready
   */
  isAvailable(): Promise<boolean>;

  /**
   * Get statistics about the RAG system
   */
  getStatistics(): Promise<RagStatistics>;
}

/**
 * FAQ entry structure
 */
export interface FaqEntry {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Result from FAQ search with relevance score
 */
export interface RelevantFaqResult {
  faqEntry: FaqEntry;
  similarityScore: number;
  relevanceRank: number;
}

/**
 * RAG system statistics
 */
export interface RagStatistics {
  totalFaqEntries: number;
  vectorDimensions: number;
  lastUpdated: string;
  indexHealth: 'healthy' | 'degraded' | 'unhealthy';
}

/**
 * Configuration for RAG service
 */
export interface RagConfig {
  vectorDbUrl: string;
  vectorDbApiKey: string;
  embeddingApiKey: string;
  embeddingModel: string;
  embeddingDimensions: number;
  indexName?: string;
  namespace?: string;
  timeout?: number;
}

/**
 * Interface for vector database operations
 */
export interface IVectorDatabase {
  /**
   * Store vectors in the database
   * @param vectors Array of vectors with metadata
   */
  upsertVectors(vectors: VectorEntry[]): Promise<void>;

  /**
   * Search for similar vectors
   * @param queryVector Query vector
   * @param topK Number of results to return
   * @param filter Optional metadata filter
   */
  searchSimilar(
    queryVector: number[],
    topK: number,
    filter?: Record<string, any>
  ): Promise<VectorSearchResult[]>;

  /**
   * Delete vectors by IDs
   * @param ids Array of vector IDs to delete
   */
  deleteVectors(ids: string[]): Promise<void>;

  /**
   * Get database statistics
   */
  getStatistics(): Promise<VectorDbStatistics>;

  /**
   * Check database health
   */
  healthCheck(): Promise<boolean>;
}

/**
 * Vector entry for storage
 */
export interface VectorEntry {
  id: string;
  vector: number[];
  metadata: Record<string, any>;
}

/**
 * Result from vector search
 */
export interface VectorSearchResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
}

/**
 * Vector database statistics
 */
export interface VectorDbStatistics {
  totalVectors: number;
  dimensions: number;
  indexSize: number;
  lastUpdated: string;
}

/**
 * Interface for embedding service
 */
export interface IEmbeddingService {
  /**
   * Generate embeddings for text
   * @param texts Array of texts to embed
   */
  generateEmbeddings(texts: string[]): Promise<number[][]>;

  /**
   * Generate embedding for a single text
   * @param text Text to embed
   */
  generateEmbedding(text: string): Promise<number[]>;

  /**
   * Get embedding dimensions
   */
  getDimensions(): number;

  /**
   * Check if the embedding service is available
   */
  isAvailable(): Promise<boolean>;
}

/**
 * Enhanced LLM service interface for RAG integration
 */
export interface IRagEnabledLlmService {
  /**
   * Generate response with RAG context
   * @param query User query
   * @param ragContext Relevant FAQ entries from RAG search
   * @param conversationHistory Previous conversation messages
   */
  generateResponseWithRagContext(
    query: string,
    ragContext: RelevantFaqResult[],
    conversationHistory?: any[]
  ): Promise<string>;

  /**
   * Determine if a query should use RAG or direct LLM response
   * @param query User query
   */
  shouldUseRag(query: string): Promise<boolean>;
}
