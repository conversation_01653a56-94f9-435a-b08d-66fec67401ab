import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useChat } from '../../contexts/ChatContext';
import MessageList from '../MessageList/MessageList';
import MessageInput from '../MessageInput/MessageInput';
import Button from '../common/Button';

const WidgetContainer = styled.div<{ isExpanded: boolean }>`
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: ${({ isExpanded }) => (isExpanded ? '400px' : '60px')};
  height: ${({ isExpanded }) => (isExpanded ? '600px' : '60px')};
  background-color: white;
  border-radius: 12px;
  box-shadow: var(--box-shadow);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
`;

const WidgetHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--primary-color);
  color: white;
`;

const HeaderTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
`;

const ToggleButton = styled(Button)`
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: white;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const ChatContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ChatWidget: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { messages, isTyping, isLoading, sendMessage, createSession, currentSession } = useChat();

  // Create a new session when the widget is first expanded
  useEffect(() => {
    if (isExpanded && !currentSession) {
      createSession().catch((error) => {
        console.error('Failed to create chat session:', error);
      });
    }
  }, [isExpanded, currentSession, createSession]);

  const toggleWidget = () => {
    setIsExpanded(!isExpanded);
  };

  const handleSendMessage = async (content: string) => {
    try {
      await sendMessage(content);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Show error notification
    }
  };

  return (
    <WidgetContainer isExpanded={isExpanded}>
      {isExpanded ? (
        <>
          <WidgetHeader>
            <HeaderTitle>AI Chat Assistant</HeaderTitle>
            <ToggleButton onClick={toggleWidget} aria-label="Minimize chat">
              −
            </ToggleButton>
          </WidgetHeader>
          <ChatContainer>
            <MessageList messages={messages} isTyping={isTyping} />
            <MessageInput
              onSendMessage={handleSendMessage}
              isDisabled={isLoading || !currentSession}
              placeholder={isLoading ? 'Loading...' : 'Type your message...'}
            />
          </ChatContainer>
        </>
      ) : (
        <ToggleButton
          onClick={toggleWidget}
          aria-label="Open chat"
          variant="primary"
          style={{ width: '60px', height: '60px', fontSize: '24px' }}
        >
          💬
        </ToggleButton>
      )}
    </WidgetContainer>
  );
};

export default ChatWidget;
