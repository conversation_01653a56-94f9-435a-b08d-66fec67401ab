import { Tool<PERSON><PERSON>, ToolCallResult } from '@otrs-ai-powered/shared';

/**
 * Interface for tool calling service that is provider-agnostic
 */
export interface IToolCallingService {
  /**
   * Execute a tool call and return the result
   */
  executeToolCall(toolCall: ToolCall): Promise<ToolCallResult>;

  /**
   * Execute multiple tool calls in parallel
   */
  executeToolCalls(toolCalls: ToolCall[]): Promise<ToolCallResult[]>;

  /**
   * Check if the service is available and ready to execute tools
   */
  isAvailable(): Promise<boolean>;
}

/**
 * Configuration for tool calling service
 */
export interface ToolCallingConfig {
  mcpServerUrl: string;
  timeout?: number;
  retryAttempts?: number;
}
