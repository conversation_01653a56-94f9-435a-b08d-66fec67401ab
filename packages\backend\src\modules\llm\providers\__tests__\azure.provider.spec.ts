import { Test, TestingModule } from '@nestjs/testing';
import { AzureProvider } from '../azure.provider';
import { LlmProviderConfig } from '../../interfaces/llm-provider.interface';
import { ChatMessage, Tool } from '@otrs-ai-powered/shared';

// Mock the Azure AI client
jest.mock('@azure-rest/ai-inference', () => {
  return jest.fn(() => ({
    path: jest.fn(() => ({
      post: jest.fn(),
    })),
  }));
});

describe('AzureProvider', () => {
  let provider: AzureProvider;
  let mockClient: any;

  const mockConfig: LlmProviderConfig = {
    apiKey: 'test-api-key',
    model: 'Phi-4-mini-instruct',
    baseUrl: 'https://otrs-phi4-resource.services.ai.azure.com/models',
    temperature: 0.7,
    maxTokens: 2000,
    timeout: 30000,
  };

  const mockAzureResponse = {
    status: '200',
    body: {
      choices: [
        {
          message: {
            role: 'assistant',
            content: 'Hello! I am an AI assistant. How can I help you today?',
          },
          finish_reason: 'stop',
        },
      ],
      usage: {
        prompt_tokens: 25,
        completion_tokens: 15,
        total_tokens: 40,
      },
    },
  };

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock client
    mockClient = {
      path: jest.fn(() => ({
        post: jest.fn().mockResolvedValue(mockAzureResponse),
      })),
    };

    // Mock the ModelClient constructor
    const ModelClient = require('@azure-rest/ai-inference');
    ModelClient.mockReturnValue(mockClient);

    provider = new AzureProvider(mockConfig);
  });

  describe('Constructor', () => {
    it('should create Azure provider with correct configuration', () => {
      expect(provider).toBeDefined();
      expect(provider.getProviderName()).toBe('Azure AI');
      expect(provider.getModel()).toBe('Phi-4-mini-instruct');
    });

    it('should throw error if baseUrl is missing', () => {
      const invalidConfig = { ...mockConfig };
      delete invalidConfig.baseUrl;

      expect(() => new AzureProvider(invalidConfig)).toThrow('Azure AI baseUrl is required');
    });
  });

  describe('generateCompletion', () => {
    it('should generate completion for simple messages', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are a helpful assistant.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'Hello, how are you?',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await provider.generateCompletion(messages);

      expect(response.content).toBe('Hello! I am an AI assistant. How can I help you today?');
      expect(response.usage).toEqual({
        promptTokens: 25,
        completionTokens: 15,
        totalTokens: 40,
      });
      expect(mockClient.path).toHaveBeenCalledWith('/chat/completions');
    });

    it('should handle tool calling scenario', async () => {
      const responseWithTools = {
        status: '200',
        body: {
          choices: [
            {
              message: {
                role: 'assistant',
                content: 'I will create a ticket for you.',
                tool_calls: [
                  {
                    id: 'call_123',
                    type: 'function',
                    function: {
                      name: 'create_ticket',
                      arguments: '{"title": "Help Request", "description": "User needs assistance"}',
                    },
                  },
                ],
              },
              finish_reason: 'tool_calls',
            },
          ],
        },
      };

      // Reset the mock for this specific test
      mockClient.path.mockReturnValue({
        post: jest.fn().mockResolvedValue(responseWithTools),
      });

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Please create a ticket for my issue.',
          timestamp: new Date().toISOString(),
        },
      ];

      const tools: Tool[] = [
        {
          type: 'function',
          name: 'create_ticket',
          description: 'Create a new OTRS ticket',
          parameters: {
            type: 'object',
            properties: {
              title: { type: 'string', description: 'Ticket title' },
              description: { type: 'string', description: 'Ticket description' },
            },
            required: ['title', 'description'],
          },
        },
      ];

      const response = await provider.generateCompletion(messages, tools);

      expect(response.content).toBe('I will create a ticket for you.');
      expect(response.toolCalls).toHaveLength(1);
      expect(response.toolCalls![0].toolId).toBe('call_123');
      expect(response.toolCalls![0].toolName).toBe('create_ticket');
      expect(response.toolCalls![0].arguments).toEqual({
        title: 'Help Request',
        description: 'User needs assistance',
      });
    });

    it('should throw error for empty messages array', async () => {
      await expect(provider.generateCompletion([])).rejects.toThrow('Messages array cannot be empty');
    });

    it('should handle API errors gracefully', async () => {
      const errorResponse = {
        status: '401',
        body: { error: 'Unauthorized' },
      };

      // Reset the mock for this specific test
      mockClient.path.mockReturnValue({
        post: jest.fn().mockResolvedValue(errorResponse),
      });

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Hello',
          timestamp: new Date().toISOString(),
        },
      ];

      await expect(provider.generateCompletion(messages)).rejects.toThrow('Azure AI API error: 401');
    });
  });

  describe('validateConfig', () => {
    it('should validate configuration successfully', async () => {
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(true);
    });

    it('should return false for invalid configuration', async () => {
      // Reset the mock for this specific test
      mockClient.path.mockReturnValue({
        post: jest.fn().mockRejectedValue(new Error('Network error')),
      });

      const isValid = await provider.validateConfig();
      expect(isValid).toBe(false);
    });
  });
});
