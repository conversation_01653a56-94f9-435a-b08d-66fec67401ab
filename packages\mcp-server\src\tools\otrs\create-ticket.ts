import { CreateTicketRequest, TicketPriority } from '@otrs-ai-powered/shared';
import { otrsApiService } from '../../services/otrs-api.service';
import { validateRequiredFields, validateStringLength, validateEnum } from '../../utils/validation';
import { withErrorHandling, ValidationError } from '../../utils/error-handling';
import { logger } from '../../utils/logger';

interface CreateTicketArgs {
  title: string;
  description: string;
  priority: string;
  queue: string;
  customer: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

/**
 * Tool to create a new ticket in OTRS
 */
export const createTicket = withErrorHandling(async (args: CreateTicketArgs) => {
  logger.info('Creating ticket with args:', args);
  
  // Validate required fields
  validateRequiredFields(args, ['title', 'description', 'priority', 'queue', 'customer']);
  
  // Validate string lengths
  validateStringLength(args.title, 'title', 5, 100);
  validateStringLength(args.description, 'description', 10, 5000);
  
  // Validate priority
  try {
    validateEnum(
      args.priority.toLowerCase() as TicketPriority,
      'priority',
      Object.values(TicketPriority)
    );
  } catch (error) {
    throw new ValidationError(
      `Invalid priority. Must be one of: ${Object.values(TicketPriority).join(', ')}`
    );
  }
  
  // Prepare ticket data
  const ticketData: CreateTicketRequest = {
    title: args.title,
    description: args.description,
    priority: args.priority.toLowerCase() as TicketPriority,
    queue: args.queue,
    customer: args.customer,
    tags: args.tags,
    customFields: args.customFields,
    attachments: [],
  };
  
  // Create ticket in OTRS
  const ticket = await otrsApiService.createTicket(ticketData);
  
  return {
    success: true,
    ticketId: ticket.id,
    message: `Ticket ${ticket.id} created successfully`,
    ticket,
  };
});
