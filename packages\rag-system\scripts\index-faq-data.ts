#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import path from 'path';
import { RagService } from '../src/retrieval/retriever.service';
import { logger } from '../src/utils/logger';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

/**
 * Complete FAQ Data Indexing Script
 * 
 * This script performs the complete workflow:
 * 1. Excel Analysis → FAQ Data Processing → Vector Database Indexing
 * 2. Processes the Excel file and extracts FAQ entries
 * 3. Generates embeddings using Azure AI
 * 4. Stores embeddings and FAQ content in Pinecone vector database
 * 
 * Usage: npx ts-node scripts/index-faq-data.ts
 */

async function main() {
  try {
    logger.info('🚀 Starting Complete FAQ Data Indexing Process...\n');

    // Configuration
    const config = {
      embedding: {
        provider: 'azure' as const,
        apiKey: process.env.EMBEDDING_API_KEY!,
        model: process.env.EMBEDDING_MODEL || 'text-embedding-3-small',
        baseUrl: process.env.EMBEDDING_BASE_URL!,
        dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '384', 10),
        batchSize: parseInt(process.env.EMBEDDING_BATCH_SIZE || '100', 10),
        timeout: parseInt(process.env.EMBEDDING_TIMEOUT || '30000', 10),
      },
      vectorDb: {
        apiKey: process.env.PINECONE_API_KEY!,
        indexName: process.env.PINECONE_INDEX_NAME!,
        dimension: parseInt(process.env.PINECONE_DIMENSION || '384', 10),
      },
      retrieval: {
        topK: parseInt(process.env.RAG_MAX_RESULTS || '5', 10),
        similarityThreshold: parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.7'),
        maxContextLength: parseInt(process.env.RAG_CONTEXT_WINDOW || '4000', 10),
        enableReranking: process.env.RAG_ENABLE_RERANKING === 'true',
      },
    };

    // Validate configuration
    logger.info('📋 Configuration:');
    logger.info(`  Embedding Provider: ${config.embedding.provider}`);
    logger.info(`  Embedding Model: ${config.embedding.model}`);
    logger.info(`  Embedding Dimensions: ${config.embedding.dimensions}`);
    logger.info(`  Vector DB Index: ${config.vectorDb.indexName}`);
    logger.info(`  Vector DB Dimension: ${config.vectorDb.dimension}`);
    logger.info('');

    if (!config.embedding.apiKey) {
      throw new Error('EMBEDDING_API_KEY is required');
    }
    if (!config.vectorDb.apiKey) {
      throw new Error('PINECONE_API_KEY is required');
    }
    if (!config.vectorDb.indexName) {
      throw new Error('PINECONE_INDEX_NAME is required');
    }

    // Initialize RAG service
    logger.info('🔧 Initializing RAG service...');
    const ragService = new RagService(config);

    // Initialize the service (this will set up vector database connection)
    await ragService.initialize();
    logger.info('✅ RAG service initialized successfully\n');

    // Check current vector database status
    logger.info('📊 Checking current vector database status...');
    try {
      const stats = await ragService.getStatistics();
      logger.info(`  Current vectors in database: ${stats.vectorCount || 0}`);
      logger.info(`  Index fullness: ${((stats.indexFullness || 0) * 100).toFixed(2)}%`);
      
      if (stats.vectorCount && stats.vectorCount > 0) {
        logger.warn(`⚠️  Vector database already contains ${stats.vectorCount} vectors`);
        logger.warn('   This operation will add more vectors to the existing data');
        logger.warn('   If you want to replace existing data, please clear the index first\n');
      }
    } catch (error) {
      logger.warn('Could not retrieve current database statistics:', error instanceof Error ? error.message : String(error));
    }

    // Start the complete indexing process
    logger.info('🔄 Starting FAQ data indexing process...');
    logger.info('   Step 1: Processing Excel file...');
    logger.info('   Step 2: Generating embeddings...');
    logger.info('   Step 3: Storing in vector database...\n');

    const startTime = Date.now();

    // This method does the complete workflow:
    // 1. Processes Excel file using DataProcessor
    // 2. Generates embeddings using EmbeddingService  
    // 3. Stores vectors in Pinecone using VectorDbService
    const result = await ragService.indexFaqData();

    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // Display results
    logger.info('\n🎉 FAQ Data Indexing Complete!\n');
    logger.info('=== INDEXING RESULTS ===');
    logger.info(`📄 Total documents processed: ${result.totalDocuments}`);
    logger.info(`✅ Successfully indexed: ${result.successfullyIndexed}`);
    logger.info(`❌ Failed to index: ${result.failed}`);
    logger.info(`📈 Success rate: ${((result.successfullyIndexed / result.totalDocuments) * 100).toFixed(1)}%`);
    logger.info(`⏱️  Total processing time: ${(totalTime / 1000).toFixed(2)} seconds`);

    if (result.errors && result.errors.length > 0) {
      logger.info('\n=== ERRORS ===');
      result.errors.forEach((error, index) => {
        logger.warn(`Error ${index + 1}: ${error}`);
      });
    }

    logger.info('\n=== PERFORMANCE STATISTICS ===');
    logger.info(`⚡ Average embedding time: ${result.statistics.averageEmbeddingTime.toFixed(2)}ms per document`);
    logger.info(`🔤 Total tokens used: ${result.statistics.totalTokensUsed}`);
    logger.info(`📂 Categories processed: ${result.statistics.categories.length}`);
    logger.info(`   Categories: ${result.statistics.categories.join(', ')}`);

    // Verify the indexing by checking final database status
    logger.info('\n📊 Final vector database status:');
    try {
      const finalStats = await ragService.getStatistics();
      logger.info(`  Total vectors in database: ${finalStats.vectorCount || 0}`);
      logger.info(`  Index fullness: ${((finalStats.indexFullness || 0) * 100).toFixed(2)}%`);
    } catch (error) {
      logger.warn('Could not retrieve final database statistics:', error instanceof Error ? error.message : String(error));
    }

    // Test a sample search to verify everything is working
    logger.info('\n🔍 Testing search functionality...');
    try {
      const testQuery = 'How do I reset my password?';
      const searchResults = await ragService.searchFaqs(testQuery, { topK: 3 });
      
      if (searchResults.results.length > 0) {
        logger.info(`✅ Search test successful! Found ${searchResults.results.length} results for: "${testQuery}"`);
        logger.info(`   Top result: "${searchResults.results[0].question}" (score: ${searchResults.results[0].score.toFixed(3)})`);
      } else {
        logger.warn('⚠️  Search test returned no results - this might indicate an issue');
      }
    } catch (error) {
      logger.warn('Search test failed:', error instanceof Error ? error.message : String(error));
    }

    logger.info('\n✨ FAQ data indexing completed successfully!');
    logger.info('   Your vector database is now populated with FAQ data and ready for use.');

  } catch (error) {
    logger.error('\n❌ FAQ data indexing failed:');
    logger.error('Error details:', error instanceof Error ? error.message : String(error));
    
    if (error instanceof Error && error.stack) {
      logger.error('Stack trace:', error.stack);
    }
    
    // Provide troubleshooting tips
    logger.info('\n🔧 Troubleshooting tips:');
    logger.info('1. Verify your environment variables in .env.local');
    logger.info('2. Check that the Excel file exists and is accessible');
    logger.info('3. Ensure Pinecone API key and index name are correct');
    logger.info('4. Verify Azure AI embedding service credentials');
    logger.info('5. Check network connectivity to external services');
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
