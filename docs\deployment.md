# Deployment Guide

This document provides instructions for deploying the OTRS AI-Powered Chat Assistant in various environments.

## Prerequisites

- Node.js (v18 or later)
- Docker and Docker Compose (for containerized deployment)
- Kubernetes (for cloud deployment)
- Access to OTRS API
- OpenAI API key or other LLM provider credentials
- Vector database (for RAG system)

## Environment Configuration

Before deployment, you need to configure the environment variables. Copy the `.env.example` file to `.env.production` and update the values:

```bash
cp .env.example .env.production
# Edit .env.production with your configuration
```

Key environment variables to configure:

- `JWT_SECRET`: Secret key for JWT token generation
- `LLM_API_KEY`: API key for the LLM provider
- `OTRS_API_URL`, `OTRS_API_USERNAME`, `OTRS_API_PASSWORD`: OTRS API credentials
- `EMBEDDING_API_KEY`: API key for embedding generation
- `VECTOR_DB_URL`, `VECTOR_DB_API_KEY`: Vector database credentials

## Local Deployment

### Build

Build all packages:

```bash
yarn build
```

### Run

Start all services:

```bash
# Frontend
yarn workspace @otrs-ai-powered/frontend start

# Backend
yarn workspace @otrs-ai-powered/backend start

# MCP Server
yarn workspace @otrs-ai-powered/mcp-server start

# RAG System
yarn workspace @otrs-ai-powered/rag-system start
```

## Docker Deployment

### Build Docker Images

Build all Docker images:

```bash
docker-compose build
```

### Run Docker Containers

Start all containers:

```bash
docker-compose up -d
```

Check container status:

```bash
docker-compose ps
```

View logs:

```bash
docker-compose logs -f
```

Stop containers:

```bash
docker-compose down
```

## Kubernetes Deployment

### Prerequisites

- Kubernetes cluster
- kubectl configured to access your cluster
- Helm (optional, for package management)

### Deployment Steps

1. Create a namespace:

```bash
kubectl create namespace otrs-ai
```

2. Create a ConfigMap for environment variables:

```bash
kubectl create configmap otrs-ai-config --from-env-file=.env.production -n otrs-ai
```

3. Create a Secret for sensitive information:

```bash
kubectl create secret generic otrs-ai-secrets \
  --from-literal=JWT_SECRET=your-jwt-secret \
  --from-literal=LLM_API_KEY=your-llm-api-key \
  --from-literal=OTRS_API_PASSWORD=your-otrs-password \
  --from-literal=VECTOR_DB_API_KEY=your-vector-db-api-key \
  -n otrs-ai
```

4. Apply Kubernetes manifests:

```bash
kubectl apply -f k8s/ -n otrs-ai
```

5. Check deployment status:

```bash
kubectl get pods -n otrs-ai
```

6. Access the services:

```bash
kubectl get services -n otrs-ai
```

## Cloud Provider Deployment

### AWS

1. Build and push Docker images to Amazon ECR
2. Deploy using ECS or EKS
3. Configure load balancers and security groups
4. Set up CloudWatch for monitoring

### Azure

1. Build and push Docker images to Azure Container Registry
2. Deploy using Azure Kubernetes Service (AKS)
3. Configure Azure Application Gateway
4. Set up Azure Monitor for monitoring

### Google Cloud

1. Build and push Docker images to Google Container Registry
2. Deploy using Google Kubernetes Engine (GKE)
3. Configure Cloud Load Balancing
4. Set up Cloud Monitoring for monitoring

## OTRS Integration

To integrate the chat widget with OTRS:

1. Add the following code to your OTRS template:

```html
<iframe 
  src="https://your-chat-assistant-url" 
  style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; z-index: 9999;"
></iframe>
```

2. Configure CORS in your backend to allow requests from the OTRS domain:

```
CORS_ORIGIN=https://your-otrs-domain.com
```

3. Ensure the OTRS API is accessible from the MCP Server:

```
OTRS_API_URL=https://your-otrs-domain.com/api
```

## Monitoring and Logging

### Logging

All components use Winston for logging. In production, logs are written to files:

- `error.log`: Error-level logs
- `combined.log`: All logs

### Monitoring

For production deployments, we recommend setting up:

1. Prometheus for metrics collection
2. Grafana for visualization
3. Alert Manager for alerting

### Health Checks

All services provide health check endpoints:

- Frontend: `/health`
- Backend: `/api/health`
- MCP Server: `/health`
- RAG System: `/health`

## Backup and Recovery

### Database Backup

If you're using a vector database for the RAG system, set up regular backups:

- Pinecone: Use Pinecone's backup features
- Weaviate: Set up regular backups of Weaviate data
- Milvus: Use Milvus's backup and restore functionality

### Configuration Backup

Regularly back up your configuration files and environment variables.

## Scaling

### Horizontal Scaling

All components can be horizontally scaled:

- Frontend: Deploy multiple instances behind a load balancer
- Backend: Deploy multiple instances with sticky sessions for WebSockets
- MCP Server: Deploy multiple instances
- RAG System: Deploy multiple instances

### Vertical Scaling

For components with higher resource requirements:

- Backend: Increase CPU and memory for handling more concurrent connections
- RAG System: Increase memory for larger vector databases

## Troubleshooting

### Common Issues

1. **Connection Issues**:
   - Check network connectivity between components
   - Verify CORS configuration
   - Check WebSocket connection

2. **Authentication Issues**:
   - Verify JWT secret is consistent across deployments
   - Check token expiration times

3. **Performance Issues**:
   - Monitor resource usage
   - Check for slow database queries
   - Optimize vector search parameters
