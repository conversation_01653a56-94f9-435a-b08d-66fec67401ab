import { ChatMessage, ChatSession, CHAT_API } from '@otrs-ai-powered/shared';
import { apiService } from './api';

export const chatService = {
  /**
   * Send a message in a chat session
   */
  sendMessage: async (sessionId: string, content: string): Promise<ChatMessage> => {
    return apiService.post<ChatMessage>(CHAT_API.SEND_MESSAGE, {
      sessionId,
      content,
    });
  },

  /**
   * Get chat history for the current user
   */
  getChatHistory: async (): Promise<ChatSession[]> => {
    return apiService.get<ChatSession[]>(CHAT_API.GET_HISTORY);
  },

  /**
   * Get a specific chat session
   */
  getSession: async (sessionId: string): Promise<ChatSession> => {
    const url = CHAT_API.GET_SESSION.replace(':sessionId', sessionId);
    return apiService.get<ChatSession>(url);
  },

  /**
   * Create a new chat session
   */
  createSession: async (): Promise<ChatSession> => {
    return apiService.post<ChatSession>(CHAT_API.CREATE_SESSION);
  },

  /**
   * Delete a chat session
   */
  deleteSession: async (sessionId: string): Promise<void> => {
    const url = CHAT_API.DELETE_SESSION.replace(':sessionId', sessionId);
    return apiService.delete<void>(url);
  },
};
