import { Controller, Get, Post, Body, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ChatService } from './chat.service';
import { CreateSessionDto, SendMessageDto } from './dto';

@Controller('chat')
@UseGuards(JwtAuthGuard)
export class ChatController {
  constructor(private chatService: ChatService) {}

  @Post('sessions')
  async createSession(@Request() req, @Body() createSessionDto: CreateSessionDto) {
    return this.chatService.createSession(req.user, createSessionDto);
  }

  @Get('sessions/:sessionId')
  async getSession(@Request() req, @Param('sessionId') sessionId: string) {
    return this.chatService.getSession(sessionId, req.user.id);
  }

  @Get('history')
  async getSessions(@Request() req) {
    return this.chatService.getSessions(req.user.id);
  }

  @Delete('sessions/:sessionId')
  async deleteSession(@Request() req, @Param('sessionId') sessionId: string) {
    await this.chatService.deleteSession(sessionId, req.user.id);
    return { message: 'Session deleted successfully' };
  }

  @Post('messages')
  async sendMessage(@Request() req, @Body() sendMessageDto: SendMessageDto) {
    return this.chatService.sendMessage(req.user, sendMessageDto);
  }
}
