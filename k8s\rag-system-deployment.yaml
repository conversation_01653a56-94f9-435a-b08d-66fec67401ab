apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-system
  labels:
    app: rag-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: rag-system
  template:
    metadata:
      labels:
        app: rag-system
    spec:
      containers:
      - name: rag-system
        image: your-org/otrs-ai-powered:latest-rag-system
        ports:
        - containerPort: 6000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "6000"
        - name: EMBEDDING_API_KEY
          valueFrom:
            secretKeyRef:
              name: otrs-ai-secrets
              key: EMBEDDING_API_KEY
        - name: EMBEDDING_MODEL
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: EMBEDDING_MODEL
        - name: EMBEDDING_DIMENSIONS
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: EMBEDDING_DIMENSIONS
        - name: VECTOR_DB_URL
          valueFrom:
            configMapKeyRef:
              name: otrs-ai-config
              key: VECTOR_DB_URL
        - name: VECTOR_DB_API_KEY
          valueFrom:
            secretKeyRef:
              name: otrs-ai-secrets
              key: VECTOR_DB_API_KEY
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "200m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 6000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 6000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: vector-db-data
          mountPath: /app/data
      volumes:
      - name: vector-db-data
        persistentVolumeClaim:
          claimName: vector-db-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: rag-system
spec:
  selector:
    app: rag-system
  ports:
  - port: 6000
    targetPort: 6000
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: vector-db-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
