import { logger } from './logger';

/**
 * Custom error class for tool execution errors
 */
export class ToolExecutionError extends Error {
  constructor(message: string, public readonly code: string = 'TOOL_EXECUTION_ERROR') {
    super(message);
    this.name = 'ToolExecutionError';
  }
}

/**
 * Custom error class for validation errors
 */
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Wraps a tool function with error handling
 * @param toolFn Tool function to wrap
 * @returns Wrapped function with error handling
 */
export function withErrorHandling<T, R>(
  toolFn: (args: T) => Promise<R>
): (args: T) => Promise<R> {
  return async (args: T): Promise<R> => {
    try {
      return await toolFn(args);
    } catch (error) {
      // Log the error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Tool execution error: ${errorMessage}`, {
        error,
        args,
      });

      // Rethrow the error with appropriate type
      if (error instanceof ValidationError || error instanceof ToolExecutionError) {
        throw error;
      } else {
        throw new ToolExecutionError(
          `An error occurred during tool execution: ${errorMessage}`
        );
      }
    }
  };
}
