FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./
COPY packages/shared/package.json ./packages/shared/
COPY packages/rag-system/package.json ./packages/rag-system/

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build shared package
RUN yarn workspace @otrs-ai-powered/shared build

# Build RAG system
RUN yarn workspace @otrs-ai-powered/rag-system build

# Production image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./
COPY packages/shared/package.json ./packages/shared/
COPY packages/rag-system/package.json ./packages/rag-system/

# Install production dependencies
RUN yarn install --frozen-lockfile --production

# Copy built files
COPY --from=builder /app/packages/shared/dist ./packages/shared/dist
COPY --from=builder /app/packages/rag-system/dist ./packages/rag-system/dist

# Expose port
EXPOSE 6000

# Start the application
CMD ["node", "packages/rag-system/dist/index.js"]
