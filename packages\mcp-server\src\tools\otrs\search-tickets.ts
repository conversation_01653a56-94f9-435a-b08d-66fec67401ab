import { otrsApiService } from '../../services/otrs-api.service';
import { validateRequiredFields, validateStringLength } from '../../utils/validation';
import { withErrorHandling } from '../../utils/error-handling';
import { logger } from '../../utils/logger';

interface SearchTicketsArgs {
  query: string;
  limit?: number;
}

/**
 * Tool to search for tickets in OTRS
 */
export const searchTickets = withErrorHandling(async (args: SearchTicketsArgs) => {
  logger.info('Searching tickets with args:', args);
  
  // Validate required fields
  validateRequiredFields(args, ['query']);
  
  // Validate query
  validateStringLength(args.query, 'query', 2, 100);
  
  // Set default limit
  const limit = args.limit || 10;
  
  // Search tickets in OTRS
  const tickets = await otrsApiService.searchTickets(args.query);
  
  // Limit the number of results
  const limitedTickets = tickets.slice(0, limit);
  
  return {
    success: true,
    message: `Found ${tickets.length} tickets matching "${args.query}"`,
    count: tickets.length,
    tickets: limitedTickets,
  };
});
