import { EmbeddingService, EmbeddingConfig } from '../embeddings/embedding.service';
import { VectorDbService, VectorDbConfig } from '../storage/vector-db.service';
import { dataProcessor } from '../indexing/data-processor';
import { FaqEntry, VectorDocument } from '../types/document';
import { logger } from '../utils/logger';

export interface RagConfig {
  embedding: EmbeddingConfig;
  vectorDb: VectorDbConfig;
  retrieval: {
    topK: number;
    similarityThreshold: number;
    maxContextLength: number;
    enableReranking: boolean;
  };
}

export interface RetrievalResult {
  id: string;
  score: number;
  question: string;
  answer: string;
  category: string;
  metadata: {
    source: string;
    lastUpdated: string;
    contentLength: number;
  };
}

export interface RagSearchResult {
  query: string;
  results: RetrievalResult[];
  totalResults: number;
  searchTime: number;
  embedding: {
    model: string;
    dimensions: number;
    tokenCount: number;
  };
}

export interface IndexingResult {
  totalDocuments: number;
  successfullyIndexed: number;
  failed: number;
  errors: string[];
  indexingTime: number;
  statistics: {
    averageEmbeddingTime: number;
    totalTokensUsed: number;
    categories: string[];
  };
}

/**
 * RAG (Retrieval-Augmented Generation) service for FAQ retrieval
 */
export class RagService {
  private readonly embeddingService: EmbeddingService;
  private readonly vectorDbService: VectorDbService;
  private readonly config: RagConfig;
  private isInitialized = false;
  private isAutoInitializing = false;

  constructor(config: RagConfig) {
    this.config = config;
    this.embeddingService = new EmbeddingService(config.embedding);
    this.vectorDbService = new VectorDbService(config.vectorDb);
  }

  /**
   * Initialize the RAG service
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing RAG service...');

      // Initialize vector database
      await this.vectorDbService.initialize();

      // Check if embedding service is available
      const embeddingAvailable = await this.embeddingService.isAvailable();
      if (!embeddingAvailable) {
        logger.warn('Embedding service is not available - some features may not work');
      }

      // Auto-populate vector database if empty
      await this.autoInitializeData();

      this.isInitialized = true;
      logger.info('RAG service initialized successfully');

    } catch (error) {
      logger.error('Error initializing RAG service:', error);
      throw error;
    }
  }

  /**
   * Auto-initialize data if vector database is empty
   */
  private async autoInitializeData(): Promise<void> {
    try {
      if (this.isAutoInitializing) {
        logger.debug('Auto-initialization already in progress, skipping...');
        return;
      }

      logger.info('Checking if auto-initialization is needed...');

      // Check if vector database is empty
      const isEmpty = await this.vectorDbService.isEmpty();

      if (isEmpty) {
        logger.info('Vector database is empty, starting auto-initialization...');

        // Check if embedding service is available before proceeding
        const embeddingAvailable = await this.embeddingService.isAvailable();
        if (!embeddingAvailable) {
          logger.warn('Embedding service not available, skipping auto-initialization');
          return;
        }

        // Set flag to prevent recursive calls
        this.isAutoInitializing = true;

        try {
          // Process and index FAQ data automatically (without calling initialize again)
          const indexingResult = await this.indexFaqDataInternal();

          logger.info(`Auto-initialization completed successfully: ${indexingResult.successfullyIndexed} documents indexed`);
        } finally {
          this.isAutoInitializing = false;
        }
      } else {
        const indexStats = await this.vectorDbService.getIndexStats();
        const vectorCount = indexStats.totalRecordCount || 0;
        logger.info(`Vector database already contains ${vectorCount} vectors, skipping auto-initialization`);
      }

    } catch (error) {
      this.isAutoInitializing = false;
      logger.warn('Auto-initialization failed, continuing without FAQ data:', error);
      // Don't throw error here - we want the service to start even if auto-init fails
    }
  }

  /**
   * Index FAQ data from Excel file
   */
  async indexFaqData(): Promise<IndexingResult> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      return await this.indexFaqDataInternal();
    } catch (error) {
      logger.error('Error indexing FAQ data:', error);
      throw error;
    }
  }

  /**
   * Internal method to index FAQ data without initialization check
   */
  private async indexFaqDataInternal(): Promise<IndexingResult> {
    try {
      logger.info('Starting FAQ data indexing...');
      const startTime = Date.now();

      // Process Excel data
      const processingResult = await dataProcessor.processExcelToFaqEntries();

      if (processingResult.successful === 0) {
        throw new Error('No FAQ entries were successfully processed');
      }

      // Convert processing result to FAQ entries
      const faqEntries: FaqEntry[] = [];

      // We need to extract the actual FAQ data from the processing result
      // For now, let's create a simple conversion from the Excel data
      const excelData = await dataProcessor.convertToFaqEntries();
      faqEntries.push(...excelData);

      const processedDocs = await dataProcessor.convertToProcessedDocuments(faqEntries);

      // Generate embeddings
      const texts = processedDocs.map(doc => doc.content);
      const embeddingResult = await this.embeddingService.generateBatchEmbeddings(texts);

      if (embeddingResult.embeddings.length === 0) {
        throw new Error('No embeddings were generated');
      }

      // Create vector documents
      const vectorDocs: VectorDocument[] = processedDocs.map((doc, index) => ({
        id: doc.id,
        vector: embeddingResult.embeddings[index],
        metadata: {
          content: doc.content,
          category: doc.metadata.category,
          question: doc.metadata.title,
          answer: doc.content.split('\n\nAnswer: ')[1] || '',
          source: doc.metadata.source,
          lastUpdated: doc.metadata.lastUpdated,
          contentLength: doc.metadata.contentLength,
        },
      }));

      // Store in vector database
      await this.vectorDbService.upsertVectors(vectorDocs);

      const indexingTime = Date.now() - startTime;

      const result: IndexingResult = {
        totalDocuments: processingResult.totalProcessed,
        successfullyIndexed: embeddingResult.processedCount,
        failed: embeddingResult.failedCount,
        errors: embeddingResult.errors.map(e => e.error),
        indexingTime,
        statistics: {
          averageEmbeddingTime: indexingTime / embeddingResult.processedCount,
          totalTokensUsed: embeddingResult.totalTokens,
          categories: processingResult.statistics.categories,
        },
      };

      logger.info(`Indexing complete: ${result.successfullyIndexed}/${result.totalDocuments} documents indexed in ${indexingTime}ms`);
      return result;

    } catch (error) {
      logger.error('Error in internal FAQ data indexing:', error);
      throw error;
    }
  }

  /**
   * Search FAQs using semantic similarity
   */
  async searchFaqs(query: string, options: {
    topK?: number;
    category?: string;
    similarityThreshold?: number;
  } = {}): Promise<RagSearchResult> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const startTime = Date.now();
      const {
        topK = this.config.retrieval.topK,
        category,
        similarityThreshold = this.config.retrieval.similarityThreshold,
      } = options;

      logger.info(`Searching FAQs for query: "${query}" (topK=${topK})`);

      // Generate embedding for the query
      const embeddingResult = await this.embeddingService.generateEmbedding(query);

      // Prepare search options
      const searchOptions = {
        topK,
        filter: category ? { category } : undefined,
        includeMetadata: true,
        includeValues: false,
      };

      // Search vector database
      const vectorResults = await this.vectorDbService.searchSimilar(
        embeddingResult.embedding,
        searchOptions
      );

      // Filter by similarity threshold and convert to retrieval results
      const results: RetrievalResult[] = vectorResults
        .filter(result => result.score >= similarityThreshold)
        .map(result => ({
          id: result.id,
          score: result.score,
          question: result.metadata.question,
          answer: result.metadata.answer,
          category: result.metadata.category,
          metadata: {
            source: result.metadata.source,
            lastUpdated: result.metadata.lastUpdated,
            contentLength: result.metadata.contentLength,
          },
        }));

      const searchTime = Date.now() - startTime;

      logger.info(`Found ${results.length} relevant FAQs in ${searchTime}ms`);

      return {
        query,
        results,
        totalResults: results.length,
        searchTime,
        embedding: {
          model: embeddingResult.model,
          dimensions: embeddingResult.embedding.length,
          tokenCount: embeddingResult.tokenCount,
        },
      };

    } catch (error) {
      logger.error('Error searching FAQs:', error);
      throw new Error(`Failed to search FAQs: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Hybrid search combining vector similarity and metadata filtering
   */
  async hybridSearch(
    query: string,
    filters: Record<string, any> = {},
    topK = 10
  ): Promise<RagSearchResult> {
    try {
      logger.info(`Performing hybrid search for: "${query}"`);

      // First, perform semantic search with higher topK to get more candidates
      const semanticResults = await this.searchFaqs(query, {
        topK: topK * 2, // Get more candidates for filtering
        similarityThreshold: 0.3, // Lower threshold for initial search
      });

      // Apply additional filters
      let filteredResults = semanticResults.results;

      if (Object.keys(filters).length > 0) {
        filteredResults = semanticResults.results.filter(result => {
          return Object.entries(filters).every(([key, value]) => {
            if (key === 'category') {
              return result.category.toLowerCase().includes(value.toLowerCase());
            }
            return result.metadata[key as keyof typeof result.metadata] === value;
          });
        });
      }

      // Re-rank and limit results
      const sortedResults = [...filteredResults].sort((a, b) => b.score - a.score);
      const finalResults = sortedResults.slice(0, topK);

      return {
        ...semanticResults,
        results: finalResults,
        totalResults: finalResults.length,
      };

    } catch (error) {
      logger.error('Error in hybrid search:', error);
      throw error;
    }
  }

  /**
   * Get available FAQ categories from the vector database
   */
  async getCategories(): Promise<string[]> {
    try {
      logger.debug('Getting available FAQ categories');

      // Query the vector database to get unique categories
      // We'll use a dummy query to get some results and extract categories
      const dummyQuery = 'help'; // Generic query to get diverse results
      const searchResults = await this.searchFaqs(dummyQuery, {
        topK: 100, // Get more results to capture more categories
        similarityThreshold: 0.0, // Very low threshold to get diverse results
      });

      // Extract unique categories from the results
      const categories = new Set<string>();
      searchResults.results.forEach(result => {
        if (result.category?.trim()) {
          categories.add(result.category.trim());
        }
      });

      const categoryList = Array.from(categories).sort((a, b) => a.localeCompare(b));
      logger.debug(`Found ${categoryList.length} categories:`, categoryList);

      return categoryList;
    } catch (error) {
      logger.error('Error getting categories:', error);
      // Fallback to known categories if query fails
      return [
        'MFA & Authentication',
        'OneDrive & SharePoint File Management',
        'Outlook & Email Security',
        'Security & Compliance',
        'Teams & Collaboration'
      ];
    }
  }

  /**
   * Check if RAG service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const embeddingAvailable = await this.embeddingService.isAvailable();
      const vectorDbAvailable = await this.vectorDbService.isAvailable();
      return embeddingAvailable && vectorDbAvailable;
    } catch (error) {
      logger.warn('RAG service availability check failed:', error);
      return false;
    }
  }

  /**
   * Get service statistics
   */
  async getStatistics(): Promise<any> {
    try {
      const indexStats = await this.vectorDbService.getIndexStats();
      const embeddingInfo = this.embeddingService.getServiceInfo();

      return {
        isInitialized: this.isInitialized,
        vectorDatabase: indexStats,
        embedding: embeddingInfo,
        config: {
          topK: this.config.retrieval.topK,
          similarityThreshold: this.config.retrieval.similarityThreshold,
          maxContextLength: this.config.retrieval.maxContextLength,
        },
      };
    } catch (error) {
      logger.error('Error getting statistics:', error);
      throw error;
    }
  }

}
