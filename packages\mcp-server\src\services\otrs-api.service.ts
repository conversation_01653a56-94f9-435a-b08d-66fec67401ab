import axios from 'axios';
import { logger } from '../utils/logger';
import { ToolExecutionError } from '../utils/error-handling';
import { OtrsTicket, CreateTicketRequest, UpdateTicketRequest, TicketStatus, TicketPriority } from '@otrs-ai-powered/shared';

// Create axios instance for OTRS API
const otrsApi = axios.create({
  baseURL: process.env.OTRS_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authentication to requests
otrsApi.interceptors.request.use((config) => {
  config.auth = {
    username: process.env.OTRS_API_USERNAME || '',
    password: process.env.OTRS_API_PASSWORD || '',
  };
  return config;
});

export const otrsApiService = {
  /**
   * Create a new ticket in OTRS
   */
  async createTicket(ticketData: CreateTicketRequest): Promise<OtrsTicket> {
    try {
      // In a real implementation, this would call the OTRS API
      // For now, we'll simulate a response

      logger.info('Creating ticket in OTRS', { ticketData });

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate a random ticket ID
      const ticketId = `TN-${Math.floor(Math.random() * 10000)}`;

      return {
        id: ticketId,
        title: ticketData.title,
        description: ticketData.description,
        status: TicketStatus.NEW,
        priority: ticketData.priority,
        queue: ticketData.queue,
        customer: ticketData.customer,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ticketData.tags || [],
        attachments: ticketData.attachments || [],
        customFields: ticketData.customFields,
      };
    } catch (error) {
      logger.error('Error creating ticket in OTRS', { error, ticketData });
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ToolExecutionError(`Failed to create ticket: ${errorMessage}`);
    }
  },

  /**
   * Update an existing ticket in OTRS
   */
  async updateTicket(ticketData: UpdateTicketRequest): Promise<OtrsTicket> {
    try {
      // In a real implementation, this would call the OTRS API
      // For now, we'll simulate a response

      logger.info('Updating ticket in OTRS', { ticketData });

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        id: ticketData.id,
        title: ticketData.title || 'Updated Ticket',
        description: ticketData.description || 'Updated description',
        status: ticketData.status || TicketStatus.OPEN,
        priority: ticketData.priority || TicketPriority.MEDIUM,
        queue: ticketData.queue || 'Support',
        customer: '<EMAIL>',
        agent: ticketData.agent,
        createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        updatedAt: new Date().toISOString(),
        tags: ticketData.tags || [],
        attachments: [],
        customFields: ticketData.customFields,
      };
    } catch (error) {
      logger.error('Error updating ticket in OTRS', { error, ticketData });
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ToolExecutionError(`Failed to update ticket: ${errorMessage}`);
    }
  },

  /**
   * Search for tickets in OTRS
   */
  async searchTickets(query: string): Promise<OtrsTicket[]> {
    try {
      // In a real implementation, this would call the OTRS API
      // For now, we'll simulate a response

      logger.info('Searching tickets in OTRS', { query });

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate some random tickets
      return Array.from({ length: 3 }, (_, i) => {
        const status = i === 0 ? TicketStatus.NEW : i === 1 ? TicketStatus.OPEN : TicketStatus.PENDING;
        const priority = i === 0 ? TicketPriority.LOW : i === 1 ? TicketPriority.MEDIUM : TicketPriority.HIGH;

        return {
          id: `TN-${Math.floor(Math.random() * 10000)}`,
          title: `Ticket ${i + 1} matching "${query}"`,
          description: `This is a sample ticket description for query "${query}"`,
          status,
          priority,
          queue: 'Support',
          customer: '<EMAIL>',
          createdAt: new Date(Date.now() - i * 86400000).toISOString(),
          updatedAt: new Date(Date.now() - i * 43200000).toISOString(),
          tags: ['sample', query],
          attachments: [],
        };
      });
    } catch (error) {
      logger.error('Error searching tickets in OTRS', { error, query });
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ToolExecutionError(`Failed to search tickets: ${errorMessage}`);
    }
  },
};
