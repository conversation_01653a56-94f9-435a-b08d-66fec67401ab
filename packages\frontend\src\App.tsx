import React from 'react';
import styled from 'styled-components';
import ChatWidget from './components/ChatWidget/ChatWidget';
import { useAuth } from './contexts/AuthContext';

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
`;

const App: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <AppContainer>
      {isAuthenticated && <ChatWidget />}
      {!isAuthenticated && (
        <div>
          {/* This would be replaced with an actual login form in a real implementation */}
          <p>Please log in to use the chat assistant.</p>
        </div>
      )}
    </AppContainer>
  );
};

export default App;
