#!/usr/bin/env ts-node

import { dataProcessor } from '../src/indexing/data-processor';
import { logger } from '../src/utils/logger';

async function main() {
  try {
    logger.info('Testing data processing pipeline...');
    
    // Process Excel data
    const result = await dataProcessor.processExcelToFaqEntries();
    
    logger.info('\n=== Processing Results ===');
    logger.info(`Total processed: ${result.totalProcessed}`);
    logger.info(`Successful: ${result.successful}`);
    logger.info(`Failed: ${result.failed}`);
    logger.info(`Success rate: ${((result.successful / result.totalProcessed) * 100).toFixed(1)}%`);
    
    logger.info('\n=== Content Statistics ===');
    logger.info(`Average question length: ${result.statistics.averageQuestionLength} characters`);
    logger.info(`Average answer length: ${result.statistics.averageAnswerLength} characters`);
    logger.info(`Number of categories: ${result.statistics.categoriesCount}`);
    logger.info(`Categories: ${result.statistics.categories.join(', ')}`);
    
    if (result.errors.length > 0) {
      logger.info('\n=== Processing Errors ===');
      result.errors.forEach((error, index) => {
        logger.warn(`Error ${index + 1} (Row ${error.index + 1}): ${error.error}`);
      });
    }
    
    // Get detailed statistics
    const stats = await dataProcessor.getProcessingStatistics();
    logger.info('\n=== Detailed Statistics ===');
    logger.info(`Error rate: ${stats.errorRate.toFixed(2)}%`);
    
    // Save results
    const fs = require('fs');
    const path = require('path');
    
    const outputPath = path.join(__dirname, '..', 'processing-results.json');
    fs.writeFileSync(outputPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      result,
      detailedStats: stats,
    }, null, 2));
    
    logger.info(`\nProcessing results saved to: ${outputPath}`);
    
  } catch (error) {
    logger.error('Data processing test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
