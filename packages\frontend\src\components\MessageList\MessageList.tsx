import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import { ChatMessage } from '@otrs-ai-powered/shared';
import MessageItem from './MessageItem';
import TypingIndicator from '../TypingIndicator/TypingIndicator';

interface MessageListProps {
  messages: ChatMessage[];
  isTyping: boolean;
}

const ListContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--secondary-color);
  text-align: center;
  padding: 0 20px;
`;

const EmptyStateTitle = styled.h3`
  margin-bottom: 8px;
  font-weight: 600;
`;

const EmptyStateText = styled.p`
  margin: 0;
  font-size: 14px;
`;

const MessageList: React.FC<MessageListProps> = ({ messages, isTyping }) => {
  const listRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change or typing indicator appears
  useEffect(() => {
    if (listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight;
    }
  }, [messages, isTyping]);

  return (
    <ListContainer ref={listRef}>
      {messages.length === 0 ? (
        <EmptyState>
          <EmptyStateTitle>Welcome to the AI Chat Assistant</EmptyStateTitle>
          <EmptyStateText>
            Ask me anything about OTRS or how I can help you with your tickets.
          </EmptyStateText>
        </EmptyState>
      ) : (
        <>
          {messages.map((message) => (
            <MessageItem key={message.id} message={message} />
          ))}
          <TypingIndicator isVisible={isTyping} />
        </>
      )}
    </ListContainer>
  );
};

export default MessageList;
