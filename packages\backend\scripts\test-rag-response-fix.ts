#!/usr/bin/env ts-node

/**
 * Test script to verify RAG response optimization
 * This script tests the fix for redundant response generation
 */

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import { ChatService } from '../src/modules/chat/chat.service';
import { RagEnhancedLlmService } from '../src/modules/llm/rag-enhanced-llm.service';
import { User } from '@otrs-ai-powered/shared';

async function testRagResponseOptimization() {
  const logger = new Logger('RAG-Response-Test');
  
  try {
    logger.log('🚀 Starting RAG response optimization test...');
    
    // Create NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get services
    const chatService = app.get(ChatService);
    const ragEnhancedLlmService = app.get(RagEnhancedLlmService);
    
    // Create test user
    const testUser: User = {
      id: 'test-user-rag-fix',
      email: '<EMAIL>',
      name: 'RAG Test User',
      role: 'user',
    };
    
    // Test questions that should trigger RAG
    const testQuestions = [
      'How do I join a Teams meeting?',
      'How can I reset my password?',
      'What are the email configuration steps?',
      'How do I troubleshoot login issues?',
      'How to configure SharePoint access?',
    ];
    
    logger.log('📋 Testing RAG response optimization...');
    
    // Create a test session
    const session = await chatService.createSession(testUser);
    logger.log(`✅ Created test session: ${session.id}`);
    
    for (const question of testQuestions) {
      try {
        logger.log(`\n🔍 Testing: "${question}"`);
        
        const response = await chatService.sendMessage(testUser, {
          sessionId: session.id,
          content: question,
        });
        
        // Analyze response
        const usedRag = response.metadata?.usedRag || false;
        const ragResultsCount = response.metadata?.ragResultsCount || 0;
        const responseLength = response.content.length;
        
        logger.log(`   ✅ Response generated successfully`);
        logger.log(`   📊 Used RAG: ${usedRag}`);
        logger.log(`   📊 RAG results count: ${ragResultsCount}`);
        logger.log(`   📊 Response length: ${responseLength} characters`);
        
        // Check for FAQ metadata leakage
        const hasMetadataLeakage = checkForMetadataLeakage(response.content);
        if (hasMetadataLeakage.found) {
          logger.error(`   ❌ METADATA LEAKAGE DETECTED: ${hasMetadataLeakage.issues.join(', ')}`);
        } else {
          logger.log(`   ✅ No metadata leakage detected`);
        }
        
        // Show response preview
        const preview = response.content.substring(0, 150) + (response.content.length > 150 ? '...' : '');
        logger.log(`   📝 Response preview: "${preview}"`);
        
        // Check response quality
        if (responseLength < 20) {
          logger.warn(`   ⚠️  Response seems too short`);
        }
        
        if (response.content.toLowerCase().includes('how can i help')) {
          logger.warn(`   ⚠️  Response contains generic greeting - possible issue`);
        }
        
      } catch (error) {
        logger.error(`   ❌ Error with question "${question}":`, error instanceof Error ? error.message : String(error));
      }
    }
    
    logger.log('\n🧪 Testing direct RAG service...');
    
    // Test RAG service directly
    try {
      const ragResults = await ragEnhancedLlmService.searchFaqs('How do I join a Teams meeting?', 3);
      logger.log(`   📊 Direct RAG search returned ${ragResults.length} results`);
      
      if (ragResults.length > 0) {
        const topResult = ragResults[0];
        logger.log(`   📊 Top result score: ${(topResult.similarityScore * 100).toFixed(1)}%`);
        logger.log(`   📊 Top result question: "${topResult.faqEntry.question}"`);
      }
    } catch (error) {
      logger.error(`   ❌ RAG service error:`, error instanceof Error ? error.message : String(error));
    }
    
    logger.log('\n✅ RAG response optimization test completed!');
    
    await app.close();
    
  } catch (error) {
    logger.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

/**
 * Check for FAQ metadata leakage in response
 */
function checkForMetadataLeakage(response: string): { found: boolean; issues: string[] } {
  const issues: string[] = [];
  
  // Check for FAQ patterns
  if (/FAQ \d+/i.test(response)) {
    issues.push('FAQ numbering');
  }
  
  if (/Relevance:\s*\d+\.?\d*%/i.test(response)) {
    issues.push('Relevance scores');
  }
  
  if (/Category:\s*\w+/i.test(response)) {
    issues.push('Category labels');
  }
  
  if (/Q:\s*/i.test(response)) {
    issues.push('Q: prefixes');
  }
  
  if (/A:\s*/i.test(response)) {
    issues.push('A: prefixes');
  }
  
  if (/RELEVANT FAQ INFORMATION/i.test(response)) {
    issues.push('FAQ information headers');
  }
  
  if (/\d+\.\d+%/.test(response)) {
    issues.push('Percentage scores');
  }
  
  return {
    found: issues.length > 0,
    issues,
  };
}

// Run the test
if (require.main === module) {
  testRagResponseOptimization().catch(console.error);
}
