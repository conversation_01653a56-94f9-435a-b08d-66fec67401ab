import { RagService } from '../retrieval/retriever.service';
import { VectorDbService } from '../storage/vector-db.service';
import { EmbeddingService } from '../embeddings/embedding.service';

// Mock the dependencies
jest.mock('../storage/vector-db.service');
jest.mock('../embeddings/embedding.service');
jest.mock('../indexing/data-processor');

describe('RAG Service Auto-Initialization', () => {
  let ragService: RagService;
  let mockVectorDbService: jest.Mocked<VectorDbService>;
  let mockEmbeddingService: jest.Mocked<EmbeddingService>;

  const mockConfig = {
    embedding: {
      provider: 'azure' as const,
      apiKey: 'test-key',
      model: 'text-embedding-3-small',
      baseUrl: 'https://test.cognitiveservices.azure.com',
      dimensions: 384,
      batchSize: 10,
      timeout: 30000,
    },
    vectorDb: {
      apiKey: 'test-pinecone-key',
      indexName: 'test-index',
      dimension: 384,
    },
    retrieval: {
      topK: 5,
      similarityThreshold: 0.7,
      maxContextLength: 4000,
      enableReranking: false,
    },
  };

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create RAG service instance
    ragService = new RagService(mockConfig);

    // Get mocked instances
    mockVectorDbService = (ragService as any).vectorDbService;
    mockEmbeddingService = (ragService as any).embeddingService;
  });

  describe('Auto-initialization Logic', () => {
    test('should auto-initialize when vector database is empty', async () => {
      // Mock vector database as empty
      mockVectorDbService.initialize.mockResolvedValue();
      mockVectorDbService.isEmpty.mockResolvedValue(true);
      mockVectorDbService.getIndexStats.mockResolvedValue({ totalVectorCount: 0 });
      
      // Mock embedding service as available
      mockEmbeddingService.isAvailable.mockResolvedValue(true);

      // Mock the indexFaqData method to avoid actual file processing
      const mockIndexingResult = {
        totalDocuments: 10,
        successfullyIndexed: 10,
        failed: 0,
        errors: [],
        indexingTime: 1000,
        statistics: {
          averageEmbeddingTime: 100,
          totalTokensUsed: 500,
          categories: ['Test Category'],
        },
      };

      // Spy on the internal indexing method
      const indexFaqDataSpy = jest.spyOn(ragService as any, 'indexFaqDataInternal').mockResolvedValue(mockIndexingResult);

      // Initialize the service
      await ragService.initialize();

      // Verify that auto-initialization was triggered
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
      expect(mockEmbeddingService.isAvailable).toHaveBeenCalled();
      expect(indexFaqDataSpy).toHaveBeenCalled();
    });

    test('should skip auto-initialization when vector database has data', async () => {
      // Mock vector database as having data
      mockVectorDbService.initialize.mockResolvedValue();
      mockVectorDbService.isEmpty.mockResolvedValue(false);
      mockVectorDbService.getIndexStats.mockResolvedValue({ totalVectorCount: 50 });
      
      // Mock embedding service as available
      mockEmbeddingService.isAvailable.mockResolvedValue(true);

      // Spy on the indexFaqData method
      const indexFaqDataSpy = jest.spyOn(ragService, 'indexFaqData');

      // Initialize the service
      await ragService.initialize();

      // Verify that auto-initialization was skipped
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
      expect(indexFaqDataSpy).not.toHaveBeenCalled();
    });

    test('should skip auto-initialization when embedding service is not available', async () => {
      // Mock vector database as empty
      mockVectorDbService.initialize.mockResolvedValue();
      mockVectorDbService.isEmpty.mockResolvedValue(true);
      
      // Mock embedding service as not available
      mockEmbeddingService.isAvailable.mockResolvedValue(false);

      // Spy on the indexFaqData method
      const indexFaqDataSpy = jest.spyOn(ragService, 'indexFaqData');

      // Initialize the service
      await ragService.initialize();

      // Verify that auto-initialization was skipped due to unavailable embedding service
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
      expect(mockEmbeddingService.isAvailable).toHaveBeenCalled();
      expect(indexFaqDataSpy).not.toHaveBeenCalled();
    });

    test('should continue initialization even if auto-initialization fails', async () => {
      // Mock vector database as empty
      mockVectorDbService.initialize.mockResolvedValue();
      mockVectorDbService.isEmpty.mockResolvedValue(true);
      
      // Mock embedding service as available
      mockEmbeddingService.isAvailable.mockResolvedValue(true);

      // Mock internal indexing method to throw an error
      const indexFaqDataSpy = jest.spyOn(ragService as any, 'indexFaqDataInternal').mockRejectedValue(new Error('Indexing failed'));

      // Initialize the service - should not throw
      await expect(ragService.initialize()).resolves.not.toThrow();

      // Verify that auto-initialization was attempted
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
      expect(mockEmbeddingService.isAvailable).toHaveBeenCalled();
      expect(indexFaqDataSpy).toHaveBeenCalled();
    });
  });

  describe('Vector Database Empty Check', () => {
    test('should correctly identify empty vector database', async () => {
      mockVectorDbService.getIndexStats.mockResolvedValue({ totalVectorCount: 0 });
      mockVectorDbService.isEmpty.mockResolvedValue(true);

      const isEmpty = await mockVectorDbService.isEmpty();

      expect(isEmpty).toBe(true);
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
    });

    test('should correctly identify non-empty vector database', async () => {
      mockVectorDbService.getIndexStats.mockResolvedValue({ totalVectorCount: 25 });
      mockVectorDbService.isEmpty.mockResolvedValue(false);

      const isEmpty = await mockVectorDbService.isEmpty();

      expect(isEmpty).toBe(false);
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
    });

    test('should handle errors gracefully when checking if empty', async () => {
      mockVectorDbService.getIndexStats.mockRejectedValue(new Error('Database error'));
      mockVectorDbService.isEmpty.mockResolvedValue(false);

      const isEmpty = await mockVectorDbService.isEmpty();

      // Should return false on error to avoid unnecessary re-indexing
      expect(isEmpty).toBe(false);
      expect(mockVectorDbService.isEmpty).toHaveBeenCalled();
    });
  });
});
