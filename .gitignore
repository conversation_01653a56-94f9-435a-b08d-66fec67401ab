# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
dist/
build/
.next/
out/

# Environment variables
.env.local
.env.test
.env.test.local
.env.development.local
.env.production.local

# Allow .env.production template files (with placeholders) but ignore any with .local suffix
# Real production values should be set via Azure App Service Configuration or GitHub Secrets

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Turbo
.turbo/

# Python (for model-tuning)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Jupyter Notebook
.ipynb_checkpoints

# Model files
*.model
*.pkl
*.joblib
models/
checkpoints/

# Data files
data/
datasets/
*.csv
# *.json
*.parquet

# Vector database files
*.db
*.sqlite
*.sqlite3

# SSL certificates
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup

# Playwright test artifacts
packages/frontend/test-results/
packages/frontend/playwright-report/
packages/frontend/debug-*.png
packages/frontend/before-*.png
packages/frontend/after-*.png
test-results/
playwright-report/
*.png
*.webm
*.mp4
*.mov
*.avi
*.mkv
