import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  IRagService,
  FaqEntry,
  RelevantFaqResult,
  RagStatistics
} from './interfaces/rag.interface';
import { RagHttpClientService } from './rag-http-client.service';

@Injectable()
export class RagService implements IRagService {
  private readonly logger = new Logger(RagService.name);
  private isInitialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly ragHttpClient: RagHttpClientService
  ) {}

  /**
   * Initialize the RAG service with FAQ data
   */
  async initializeWithFaqData(faqData: FaqEntry[]): Promise<void> {
    try {
      this.logger.log('Initializing RAG service with FAQ data...');

      // Initialize the RAG system via HTTP API
      await this.ragHttpClient.initializeWithFaqData(faqData);

      this.isInitialized = true;
      this.logger.log('RAG service initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize RAG service:', error);
      throw error;
    }
  }

  /**
   * Search for relevant FAQ entries based on user query
   */
  async searchRelevantFaqs(query: string, topK = 5): Promise<RelevantFaqResult[]> {
    try {
      if (!this.isInitialized) {
        await this.initializeWithFaqData([]);
      }

      this.logger.debug(`Searching FAQs for query: "${query}"`);

      // Use HTTP client to search FAQs
      const results = await this.ragHttpClient.searchRelevantFaqs(query, topK);

      this.logger.debug(`Found ${results.length} relevant FAQs`);
      return results;

    } catch (error) {
      this.logger.error('Error searching FAQs:', error);
      throw error;
    }
  }

  /**
   * Add new FAQ entries to the vector database
   */
  async addFaqEntries(faqEntries: FaqEntry[]): Promise<void> {
    try {
      this.logger.log(`Adding ${faqEntries.length} FAQ entries`);
      await this.ragHttpClient.addFaqEntries(faqEntries);
    } catch (error) {
      this.logger.error('Error adding FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Update existing FAQ entries in the vector database
   */
  async updateFaqEntries(faqEntries: FaqEntry[]): Promise<void> {
    try {
      this.logger.log(`Updating ${faqEntries.length} FAQ entries`);
      await this.ragHttpClient.updateFaqEntries(faqEntries);
    } catch (error) {
      this.logger.error('Error updating FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Delete FAQ entries from the vector database
   */
  async deleteFaqEntries(faqIds: string[]): Promise<void> {
    try {
      this.logger.log(`Deleting ${faqIds.length} FAQ entries`);
      await this.ragHttpClient.deleteFaqEntries(faqIds);
    } catch (error) {
      this.logger.error('Error deleting FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Check if the RAG service is available and ready
   */
  async isAvailable(): Promise<boolean> {
    try {
      const health = await this.ragHttpClient.checkHealth();
      return health.status === 'healthy';
    } catch (error) {
      this.logger.warn('RAG service availability check failed:', error);
      return false;
    }
  }

  /**
   * Get statistics about the RAG system
   */
  async getStatistics(): Promise<RagStatistics> {
    try {
      return await this.ragHttpClient.getStatistics();
    } catch (error) {
      this.logger.error('Error getting RAG statistics:', error);
      return {
        totalFaqEntries: 0,
        vectorDimensions: 384,
        lastUpdated: new Date().toISOString(),
        indexHealth: 'unhealthy',
      };
    }
  }

  /**
   * Get available FAQ categories
   */
  async getCategories(): Promise<string[]> {
    try {
      if (!this.isInitialized) {
        await this.initializeWithFaqData([]);
      }

      return await this.ragHttpClient.getCategories();
    } catch (error) {
      this.logger.error('Error getting categories:', error);
      return [];
    }
  }

  /**
   * Perform hybrid search with filters
   */
  async hybridSearch(
    query: string,
    filters: Record<string, any> = {},
    topK = 5
  ): Promise<RelevantFaqResult[]> {
    try {
      if (!this.isInitialized) {
        await this.initializeWithFaqData([]);
      }

      // For now, use regular search as hybrid search endpoint may not be implemented
      // TODO: Implement hybrid search endpoint in RAG system
      return await this.searchRelevantFaqs(query, topK);

    } catch (error) {
      this.logger.error('Error in hybrid search:', error);
      throw error;
    }
  }

  /**
   * Initialize the service on module startup
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('RAG service module initializing...');
      
      // Check if we should auto-initialize
      const autoInit = this.configService.get<string>('RAG_AUTO_INIT') === 'true';
      if (autoInit) {
        await this.initializeWithFaqData([]);
      }

    } catch (error) {
      this.logger.warn('RAG service auto-initialization failed:', error);
    }
  }
}
