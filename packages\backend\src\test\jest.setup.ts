/**
 * Jest Setup File
 *
 * Loads test environment configuration before running tests.
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load test environment variables from .env.local
const envPath = join(__dirname, '../../.env.local');
config({ path: envPath });

// Set NODE_ENV to test if not already set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'test';
}

console.log('Jest setup: Loaded test environment from', envPath);
console.log('Jest setup: NODE_ENV =', process.env.NODE_ENV);
