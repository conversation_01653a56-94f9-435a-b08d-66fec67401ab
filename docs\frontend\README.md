# Frontend Documentation

This directory contains documentation for the frontend component of the OTRS AI-Powered Chat Assistant.

## Overview

The frontend is built with React and TypeScript, using Vite as the build tool. It provides a collapsible chat widget that can be embedded within the OTRS interface.

## Key Features

- Collapsible chat widget
- Real-time messaging with typing indicators
- Message history display
- Authentication integration
- Accessibility support
- Internationalization

## Directory Structure

```
packages/frontend/
├── public/                        # Static assets
├── src/
│   ├── components/                # Reusable UI components
│   │   ├── ChatWidget/            # Main chat widget component
│   │   ├── MessageList/           # Message history display
│   │   ├── MessageInput/          # User input component
│   │   ├── TypingIndicator/       # Typing animation component
│   │   └── common/                # Common UI elements (buttons, etc.)
│   ├── hooks/                     # Custom React hooks
│   ├── contexts/                  # React context providers
│   │   ├── ChatContext.tsx        # Chat state management
│   │   └── AuthContext.tsx        # Authentication state
│   ├── services/                  # API services
│   │   ├── api.ts                 # API client setup
│   │   ├── chatService.ts         # Chat-related API calls
│   │   └── authService.ts         # Authentication API calls
│   ├── types/                     # TypeScript type definitions
│   ├── utils/                     # Utility functions
│   ├── i18n/                      # Internationalization
│   ├── styles/                    # Global styles and themes
│   ├── App.tsx                    # Main application component
│   └── index.tsx                  # Entry point
└── ...
```

## Getting Started

### Development

```bash
# Start the development server
yarn workspace @otrs-ai-powered/frontend dev
```

### Building

```bash
# Build for production
yarn workspace @otrs-ai-powered/frontend build
```

### Testing

```bash
# Run tests
yarn workspace @otrs-ai-powered/frontend test
```

## Integration with OTRS

To integrate the chat widget with OTRS, you can use an iframe:

```html
<iframe 
  src="https://your-chat-assistant-url" 
  style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; z-index: 9999;"
></iframe>
```

Alternatively, you can include the widget as a script:

```html
<script src="https://your-chat-assistant-url/widget.js"></script>
<script>
  OTRSChatAssistant.init({
    position: 'bottom-right',
    theme: 'light',
    apiUrl: 'https://your-backend-url'
  });
</script>
```

## Component Documentation

For detailed documentation on individual components, see:

- [ChatWidget](./components/ChatWidget.md)
- [MessageList](./components/MessageList.md)
- [MessageInput](./components/MessageInput.md)
- [TypingIndicator](./components/TypingIndicator.md)

## State Management

The application uses React Context API for state management. The main contexts are:

- **AuthContext**: Manages authentication state and user information
- **ChatContext**: Manages chat sessions, messages, and WebSocket connections
