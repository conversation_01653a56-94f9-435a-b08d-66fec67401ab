import React from 'react';
import styled from 'styled-components';
import { ChatMessage } from '@otrs-ai-powered/shared';
import { formatDate } from '@otrs-ai-powered/shared';

interface MessageItemProps {
  message: ChatMessage;
}

const MessageContainer = styled.div<{ isUser: boolean }>`
  display: flex;
  flex-direction: ${({ isUser }) => (isUser ? 'row-reverse' : 'row')};
  margin-bottom: 16px;
`;

const Avatar = styled.div<{ isUser: boolean }>`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${({ isUser }) => (isUser ? 'var(--primary-color)' : 'var(--secondary-color)')};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin: ${({ isUser }) => (isUser ? '0 0 0 12px' : '0 12px 0 0')};
`;

const MessageContent = styled.div<{ isUser: boolean }>`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  background-color: ${({ isUser }) => (isUser ? 'var(--primary-color)' : '#f0f0f0')};
  color: ${({ isUser }) => (isUser ? 'white' : 'var(--text-color)')};
  position: relative;
`;

const MessageText = styled.p`
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
`;

const Timestamp = styled.span<{ isUser: boolean }>`
  font-size: 12px;
  color: ${({ isUser }) => (isUser ? 'rgba(255, 255, 255, 0.7)' : 'var(--secondary-color)')};
  margin-top: 4px;
  display: block;
  text-align: ${({ isUser }) => (isUser ? 'right' : 'left')};
`;

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.role === 'user';
  
  // Get first letter of role for avatar
  const avatarText = message.role.charAt(0).toUpperCase();
  
  return (
    <MessageContainer isUser={isUser}>
      <Avatar isUser={isUser}>{avatarText}</Avatar>
      <MessageContent isUser={isUser}>
        <MessageText>{message.content}</MessageText>
        <Timestamp isUser={isUser}>{formatDate(message.timestamp)}</Timestamp>
      </MessageContent>
    </MessageContainer>
  );
};

export default MessageItem;
