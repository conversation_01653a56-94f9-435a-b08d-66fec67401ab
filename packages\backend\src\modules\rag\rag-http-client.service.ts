import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosError } from 'axios';
import {
  Faq<PERSON><PERSON>ry,
  RelevantFaqResult,
  RagStatistics
} from './interfaces/rag.interface';

/**
 * HTTP client service for communicating with the RAG system microservice
 */
@Injectable()
export class RagHttpClientService {
  private readonly logger = new Logger(RagHttpClientService.name);
  private readonly httpClient: AxiosInstance;
  private readonly ragSystemUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.ragSystemUrl = this.configService.get<string>('RAG_SYSTEM_URL') || 'http://localhost:6000';
    
    this.httpClient = axios.create({
      baseURL: this.ragSystemUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request/response interceptors for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`Making request to RAG system: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`RAG system response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error: AxiosError) => {
        this.logger.error(`RAG system error: ${error.response?.status} ${error.message}`);
        return Promise.reject(this.handleHttpError(error));
      }
    );
  }

  /**
   * Initialize the RAG system with FAQ data
   */
  async initializeWithFaqData(faqData: FaqEntry[]): Promise<void> {
    try {
      await this.httpClient.post('/api/initialize', { faqData });
      this.logger.log('RAG system initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize RAG system:', error);
      throw error;
    }
  }

  /**
   * Search for relevant FAQ entries
   */
  async searchRelevantFaqs(query: string, topK?: number): Promise<RelevantFaqResult[]> {
    try {
      const response = await this.httpClient.post('/api/query', {
        query,
        topK: topK || 5,
      });

      // The RAG system returns a RagSearchResult object with results array
      const ragSearchResult = response.data;
      const retrievalResults = ragSearchResult.results || [];

      // Transform RetrievalResult[] to RelevantFaqResult[]
      return retrievalResults.map((result: any, index: number) => ({
        faqEntry: {
          id: result.id,
          question: result.question,
          answer: result.answer,
          category: result.category,
          metadata: result.metadata,
        },
        similarityScore: result.score,
        relevanceRank: index + 1,
      }));
    } catch (error) {
      this.logger.error('Failed to search relevant FAQs:', error);
      throw error;
    }
  }

  /**
   * Add new FAQ entries to the vector database
   */
  async addFaqEntries(faqEntries: FaqEntry[]): Promise<void> {
    try {
      await this.httpClient.post('/api/add-entries', { faqEntries });
      this.logger.log(`Added ${faqEntries.length} FAQ entries to RAG system`);
    } catch (error) {
      this.logger.error('Failed to add FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Update existing FAQ entries
   */
  async updateFaqEntries(faqEntries: FaqEntry[]): Promise<void> {
    try {
      await this.httpClient.put('/api/update-entries', { faqEntries });
      this.logger.log(`Updated ${faqEntries.length} FAQ entries in RAG system`);
    } catch (error) {
      this.logger.error('Failed to update FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Delete FAQ entries by IDs
   */
  async deleteFaqEntries(entryIds: string[]): Promise<void> {
    try {
      await this.httpClient.delete('/api/delete-entries', { data: { entryIds } });
      this.logger.log(`Deleted ${entryIds.length} FAQ entries from RAG system`);
    } catch (error) {
      this.logger.error('Failed to delete FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Get available FAQ categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const response = await this.httpClient.get('/api/categories');
      return response.data.categories || [];
    } catch (error) {
      this.logger.error('Failed to get FAQ categories:', error);
      throw error;
    }
  }

  /**
   * Get RAG system statistics
   */
  async getStatistics(): Promise<RagStatistics> {
    try {
      const response = await this.httpClient.get('/api/statistics');
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get RAG statistics:', error);
      throw error;
    }
  }

  /**
   * Check RAG system health
   */
  async checkHealth(): Promise<{ status: string; message?: string }> {
    try {
      const response = await this.httpClient.get('/api/health');
      return response.data;
    } catch (error) {
      this.logger.error('RAG system health check failed:', error);
      return { status: 'unhealthy', message: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Handle HTTP errors and convert them to appropriate exceptions
   */
  private handleHttpError(error: AxiosError): HttpException {
    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const message = (error.response.data as any)?.message || error.message;
      
      switch (status) {
        case 400:
          return new HttpException(`RAG system bad request: ${message}`, HttpStatus.BAD_REQUEST);
        case 404:
          return new HttpException(`RAG system resource not found: ${message}`, HttpStatus.NOT_FOUND);
        case 500:
          return new HttpException(`RAG system internal error: ${message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        default:
          return new HttpException(`RAG system error: ${message}`, status);
      }
    } else if (error.request) {
      // Request was made but no response received
      return new HttpException(
        'RAG system is not responding. Please check if the service is running.',
        HttpStatus.SERVICE_UNAVAILABLE
      );
    } else {
      // Something else happened
      return new HttpException(
        `RAG system communication error: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
