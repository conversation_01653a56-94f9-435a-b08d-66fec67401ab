# MCP Server Documentation

This directory contains documentation for the MCP Server component of the OTRS AI-Powered Chat Assistant.

## Overview

The MCP (Model Control Protocol) Server is built with TypeScript and provides a set of tools that can be called by the LLM to perform actions in the OTRS system, such as creating tickets, updating tickets, and searching for tickets.

## Key Features

- Tool implementation for OTRS integration
- Function calling architecture for LLM
- Input validation and error handling
- Logging and monitoring

## Directory Structure

```
packages/mcp-server/
├── src/
│   ├── index.ts                   # Entry point
│   ├── config/                    # Configuration
│   │   └── mcp.config.ts          # MCP configuration
│   ├── tools/                     # Tool implementations
│   │   ├── otrs/                  # OTRS-related tools
│   │   │   ├── create-ticket.ts   # Ticket creation tool
│   │   │   ├── update-ticket.ts   # Ticket update tool
│   │   │   └── search-tickets.ts  # Ticket search tool
│   │   ├── knowledge-base/        # Knowledge base tools
│   │   └── user-management/       # User management tools
│   ├── services/                  # Service integrations
│   │   ├── otrs-api.service.ts    # OTRS API client
│   │   └── rag.service.ts         # RAG system client
│   ├── utils/                     # Utility functions
│   │   ├── validation.ts          # Input validation
│   │   └── error-handling.ts      # Error handling utilities
│   └── types/                     # TypeScript type definitions
└── ...
```

## Getting Started

### Development

```bash
# Start the development server
yarn workspace @otrs-ai-powered/mcp-server dev
```

### Building

```bash
# Build for production
yarn workspace @otrs-ai-powered/mcp-server build
```

### Testing

```bash
# Run tests
yarn workspace @otrs-ai-powered/mcp-server test
```

## API Documentation

The MCP Server provides the following API endpoints:

### Tool Execution

- `POST /api/execute-tool`: Execute a tool

Request body:
```json
{
  "toolName": "create-ticket",
  "arguments": {
    "title": "Sample Ticket",
    "description": "This is a sample ticket",
    "priority": "medium",
    "queue": "Support",
    "customer": "<EMAIL>"
  }
}
```

Response:
```json
{
  "success": true,
  "result": {
    "ticketId": "TN-1234",
    "message": "Ticket TN-1234 created successfully",
    "ticket": {
      "id": "TN-1234",
      "title": "Sample Ticket",
      "description": "This is a sample ticket",
      "status": "new",
      "priority": "medium",
      "queue": "Support",
      "customer": "<EMAIL>",
      "createdAt": "2023-08-15T12:34:56Z",
      "updatedAt": "2023-08-15T12:34:56Z"
    }
  }
}
```

### Health Check

- `GET /health`: Check the health of the MCP Server

## Available Tools

### OTRS Tools

#### Create Ticket

Creates a new ticket in the OTRS system.

```typescript
interface CreateTicketArgs {
  title: string;
  description: string;
  priority: string;
  queue: string;
  customer: string;
  tags?: string[];
  customFields?: Record<string, any>;
}
```

#### Update Ticket

Updates an existing ticket in the OTRS system.

```typescript
interface UpdateTicketArgs {
  ticketId: string;
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  queue?: string;
  agent?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}
```

#### Search Tickets

Searches for tickets in the OTRS system.

```typescript
interface SearchTicketsArgs {
  query: string;
  limit?: number;
}
```

## Error Handling

The MCP Server uses a standardized error handling approach. All errors are logged and returned in a consistent format:

```json
{
  "error": "Error message"
}
```

For tool execution errors, the response includes more details:

```json
{
  "error": "Error message",
  "toolName": "create-ticket",
  "arguments": {
    // Original arguments
  }
}
```
