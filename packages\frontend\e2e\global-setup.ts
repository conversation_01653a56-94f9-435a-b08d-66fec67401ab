/**
 * Playwright Global Setup
 * 
 * Performs setup tasks before running E2E tests, including:
 * - Environment validation
 * - Service health checks
 * - Test data preparation
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test global setup...');

  // Environment configuration
  const baseUrl = process.env.E2E_BASE_URL || 'http://localhost:3000';
  const apiUrl = process.env.E2E_API_URL || 'http://localhost:4000';
  const skipExternalServices = process.env.SKIP_EXTERNAL_SERVICES === 'true';

  console.log(`📍 Base URL: ${baseUrl}`);
  console.log(`📍 API URL: ${apiUrl}`);
  console.log(`🔧 Skip external services: ${skipExternalServices}`);

  if (skipExternalServices) {
    console.log('⚠️  External services disabled - some tests will be skipped');
    return;
  }

  // Launch browser for setup tasks
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Health check: Frontend
    console.log('🔍 Checking frontend health...');
    try {
      await page.goto(baseUrl, { timeout: 30000 });
      await page.waitForLoadState('networkidle', { timeout: 30000 });
      console.log('✅ Frontend is accessible');
    } catch (error) {
      console.error('❌ Frontend health check failed:', error);
      throw new Error(`Frontend not accessible at ${baseUrl}`);
    }

    // Health check: Backend API
    console.log('🔍 Checking backend API health...');
    try {
      const response = await page.request.get(`${apiUrl}/api/health`);
      if (response.ok()) {
        console.log('✅ Backend API is healthy');
      } else {
        console.warn(`⚠️  Backend API returned status: ${response.status()}`);
      }
    } catch (error) {
      console.warn('⚠️  Backend API health check failed:', error);
      // Don't fail setup for API issues - tests can handle this
    }

    // Check RAG system availability
    console.log('🔍 Checking RAG system availability...');
    try {
      const response = await page.request.get(`${apiUrl}/api/rag/statistics`);
      if (response.ok()) {
        const stats = await response.json();
        console.log('✅ RAG system is available');
        console.log(`📊 RAG Stats: ${stats.statistics?.totalFaqEntries || 0} FAQ entries`);
      } else {
        console.warn(`⚠️  RAG system returned status: ${response.status()}`);
      }
    } catch (error) {
      console.warn('⚠️  RAG system check failed:', error);
    }

    // Initialize RAG system if needed
    console.log('🔧 Initializing RAG system for tests...');
    try {
      const response = await page.request.post(`${apiUrl}/api/rag/initialize`, {
        data: { faqData: [] },
        timeout: 60000,
      });
      if (response.ok()) {
        console.log('✅ RAG system initialized');
      } else {
        console.warn(`⚠️  RAG initialization returned status: ${response.status()}`);
      }
    } catch (error) {
      console.warn('⚠️  RAG initialization failed:', error);
    }

    // Prepare test data if needed
    console.log('📝 Preparing test data...');
    // Add any test data preparation here

    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;
