# Microservices Testing Strategy

## Overview

This document outlines the testing strategy for the OTRS AI-Powered system's microservices architecture, focusing on proper separation of concerns and effective testing practices for distributed systems.

## Architecture Overview

The system consists of the following microservices:
- **Backend**: NestJS API server handling business logic
- **RAG System**: Independent microservice for vector search and FAQ retrieval
- **MCP Server**: Model Context Protocol server for tool integration
- **Frontend**: React web application

## Testing Principles

### 1. Separation of Concerns

Each microservice should be tested independently:
- **Backend tests** focus on business logic, HTTP routing, authentication, and API contracts
- **RAG system tests** focus on vector search, embedding generation, and data processing
- **MCP server tests** focus on tool execution and protocol compliance
- **Frontend tests** focus on UI components, user interactions, and state management

### 2. Test Pyramid Structure

```
    E2E Tests (Few)
   ─────────────────
  Integration Tests (Some)
 ─────────────────────────
Unit Tests (Many)
```

## Testing Levels

### Unit Tests

**Purpose**: Test individual components in isolation

**Backend Unit Tests**:
- Service methods and business logic
- Controller request/response handling
- Validation and error handling
- Authentication and authorization logic
- Mock external dependencies (RAG system, MCP server)

**RAG System Unit Tests**:
- Embedding generation
- Vector database operations
- Data processing pipelines
- Search algorithms
- Configuration validation

**Example Backend Unit Test**:
```typescript
describe('ChatService', () => {
  let service: ChatService;
  let mockRagService: jest.Mocked<RagService>;

  beforeEach(() => {
    mockRagService = {
      searchRelevantFaqs: jest.fn(),
      isAvailable: jest.fn(),
    } as any;

    service = new ChatService(mockRagService);
  });

  it('should handle chat messages with RAG integration', async () => {
    mockRagService.searchRelevantFaqs.mockResolvedValue([]);
    
    const result = await service.processMessage('test message');
    
    expect(result).toBeDefined();
    expect(mockRagService.searchRelevantFaqs).toHaveBeenCalled();
  });
});
```

### Integration Tests

**Purpose**: Test interactions between components within the same service

**Backend Integration Tests**:
- HTTP client communication with RAG system
- Database operations
- Authentication flows
- API endpoint functionality with mocked external services

**RAG System Integration Tests**:
- End-to-end RAG workflow
- Vector database integration
- Embedding service integration
- Data processing pipelines

**Example Backend Integration Test**:
```typescript
describe('RAG HTTP Client Integration', () => {
  it('should communicate with RAG system via HTTP', async () => {
    // Test HTTP client configuration and error handling
    const client = new RagHttpClientService(configService);
    const health = await client.checkHealth();
    
    expect(typeof health.status).toBe('string');
  });
});
```

### End-to-End Tests

**Purpose**: Test complete user workflows across all services

**Full System E2E Tests**:
- User authentication and session management
- Complete chat workflows with RAG integration
- FAQ search and response generation
- Tool execution via MCP server
- Error handling across service boundaries

**Example E2E Test**:
```typescript
describe('Complete Chat Workflow', () => {
  it('should handle user question with RAG-enhanced response', async () => {
    // 1. User logs in
    await loginUser();
    
    // 2. User asks FAQ question
    const response = await sendChatMessage('How do I reset my password?');
    
    // 3. System searches RAG system
    // 4. System generates enhanced response
    // 5. User receives helpful answer
    expect(response.message).toContain('password');
    expect(response.usedRag).toBe(true);
  });
});
```

## Testing Environment Configuration

### Environment Files

Each service uses `.env.local` for both development and testing:

```bash
# Backend .env.local
NODE_ENV=development
RAG_SYSTEM_URL=http://localhost:6000
LLM_PROVIDER=vllm
LLM_API_KEY=test-key
# ... other config

# RAG System .env.local
NODE_ENV=development
PINECONE_API_KEY=your-pinecone-key
EMBEDDING_PROVIDER=vllm
# ... other config
```

### Test Configuration

Use environment variables to control test behavior:

```typescript
// Skip external service tests in CI
const SKIP_EXTERNAL_SERVICES = process.env.SKIP_EXTERNAL_SERVICES === 'true';

// Use different timeouts for different test types
const UNIT_TEST_TIMEOUT = 5000;
const INTEGRATION_TEST_TIMEOUT = 30000;
const E2E_TEST_TIMEOUT = 60000;
```

## Service-Specific Testing Guidelines

### Backend Testing

**Focus Areas**:
- Business logic validation
- API contract compliance
- Authentication and authorization
- Error handling and edge cases
- HTTP client communication (mocked for unit tests)

**What NOT to test**:
- RAG system's search quality
- Vector database performance
- Embedding generation accuracy

**Mock Strategy**:
```typescript
// Mock RAG HTTP client for unit tests
const mockRagClient = {
  searchRelevantFaqs: jest.fn().mockResolvedValue([]),
  getStatistics: jest.fn().mockResolvedValue({ totalFaqEntries: 0 }),
  checkHealth: jest.fn().mockResolvedValue({ status: 'healthy' }),
};
```

### RAG System Testing

**Focus Areas**:
- Vector search accuracy and performance
- Embedding generation quality
- Data processing pipelines
- Vector database operations
- API endpoint functionality

**What NOT to test**:
- Backend business logic
- User authentication
- Frontend interactions

### Integration Testing Strategy

**Cross-Service Communication**:
- Test HTTP API contracts between services
- Verify error handling when services are unavailable
- Test timeout and retry mechanisms
- Validate data serialization/deserialization

**Example Integration Test Setup**:
```typescript
describe('Microservice Integration', () => {
  beforeAll(async () => {
    // Start test instances of required services
    await startTestServices(['rag-system']);
  });

  afterAll(async () => {
    // Clean up test services
    await stopTestServices();
  });

  it('should handle service communication', async () => {
    // Test actual HTTP communication
    const response = await backendService.searchFaqs('test query');
    expect(response).toBeDefined();
  });
});
```

## Best Practices

### 1. Test Isolation

- Each test should be independent
- Clean up data between tests
- Use separate test databases/indexes
- Mock external dependencies appropriately

### 2. Test Data Management

- Use factories for test data creation
- Maintain separate test datasets
- Clean up after each test run
- Use deterministic test data

### 3. Performance Testing

- Include performance benchmarks for critical paths
- Test with realistic data volumes
- Monitor test execution times
- Set appropriate timeouts

### 4. Error Scenario Testing

- Test network failures between services
- Test service unavailability scenarios
- Test timeout handling
- Test malformed data handling

### 5. Continuous Integration

- Run unit tests on every commit
- Run integration tests on pull requests
- Run E2E tests on deployment
- Fail fast on test failures

## Test Organization

```
packages/
├── backend/
│   ├── src/
│   │   └── **/*.spec.ts          # Unit tests
│   └── test/
│       └── **/*.integration.ts   # Integration tests
├── rag-system/
│   ├── src/
│   │   └── **/*.test.ts          # Unit tests
│   └── __tests__/
│       └── **/*.integration.ts   # Integration tests
└── e2e/
    └── **/*.e2e.ts               # End-to-end tests
```

## Conclusion

This testing strategy ensures:
- Clear separation of concerns between microservices
- Appropriate test coverage at each level
- Maintainable and reliable test suites
- Effective validation of service interactions
- Confidence in system reliability and performance

Each service maintains its own test suite focused on its specific responsibilities, while integration and E2E tests validate the system as a whole.
