import React from 'react';
import styled, { css } from 'styled-components';

type ButtonVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const getButtonStyles = (variant: ButtonVariant) => {
  switch (variant) {
    case 'primary':
      return css`
        background-color: var(--primary-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: var(--primary-hover);
        }
      `;
    case 'secondary':
      return css`
        background-color: var(--secondary-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: #5a6268;
        }
      `;
    case 'success':
      return css`
        background-color: var(--success-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: #218838;
        }
      `;
    case 'danger':
      return css`
        background-color: var(--danger-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: #c82333;
        }
      `;
    case 'warning':
      return css`
        background-color: var(--warning-color);
        color: #212529;
        &:hover:not(:disabled) {
          background-color: #e0a800;
        }
      `;
    case 'info':
      return css`
        background-color: var(--info-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: #138496;
        }
      `;
    case 'light':
      return css`
        background-color: var(--light-color);
        color: #212529;
        &:hover:not(:disabled) {
          background-color: #e2e6ea;
        }
      `;
    case 'dark':
      return css`
        background-color: var(--dark-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: #23272b;
        }
      `;
    default:
      return css`
        background-color: var(--primary-color);
        color: white;
        &:hover:not(:disabled) {
          background-color: var(--primary-hover);
        }
      `;
  }
};

const getButtonSize = (size: ButtonSize) => {
  switch (size) {
    case 'small':
      return css`
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
      `;
    case 'medium':
      return css`
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
      `;
    case 'large':
      return css`
        padding: 0.5rem 1rem;
        font-size: 1.25rem;
      `;
    default:
      return css`
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
      `;
  }
};

const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  
  ${({ variant = 'primary' }) => getButtonStyles(variant)}
  ${({ size = 'medium' }) => getButtonSize(size)}
  ${({ fullWidth }) => fullWidth && css`width: 100%;`}
  
  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
  
  .button-left-icon {
    margin-right: 0.5rem;
  }
  
  .button-right-icon {
    margin-left: 0.5rem;
  }
  
  .loading-spinner {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && (
        <span className="loading-spinner">⟳</span>
      )}
      {!isLoading && leftIcon && (
        <span className="button-left-icon">{leftIcon}</span>
      )}
      {children}
      {!isLoading && rightIcon && (
        <span className="button-right-icon">{rightIcon}</span>
      )}
    </StyledButton>
  );
};

export default Button;
