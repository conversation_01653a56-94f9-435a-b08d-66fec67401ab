# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - otrs-app-backend

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: "22.x"
          cache: "yarn"
          cache-dependency-path: yarn.lock

      - name: Build packages with TypeScript composite builds
        run: |
          yarn install --frozen-lockfile
          yarn workspace @otrs-ai-powered/shared build
          yarn workspace @otrs-ai-powered/backend build

      - name: Create deployment package
        run: |
          mkdir -p deployment
          cp -r packages/backend/dist deployment/
          cp packages/backend/.env.production deployment/.env.local

          # Create deployment package.json
          cat > deployment/package.json << 'EOF'
          {
            "name": "@otrs-ai-powered/backend-deployment",
            "version": "0.1.0",
            "private": true,
            "main": "dist/main.js",
            "scripts": {
              "start": "node dist/main.js"
            },
            "engines": {
              "node": ">=22.0.0"
            }
          }
          EOF

      - name: Install production dependencies
        run: |
          cd deployment
          yarn install --production --frozen-lockfile

          # Copy shared package for runtime resolution
          mkdir -p node_modules/@otrs-ai-powered
          cp -r ../packages/shared node_modules/@otrs-ai-powered/shared

      - name: Verify deployment package
        run: |
          cd deployment

          # Test shared package resolution
          node -e "
            try {
              const shared = require('@otrs-ai-powered/shared');
              console.log('✅ Shared package resolved successfully');
            } catch (error) {
              console.error('❌ Failed to resolve shared package:', error.message);
              process.exit(1);
            }
          "

          # Verify critical files exist
          test -f dist/main.js || { echo "❌ main.js not found"; exit 1; }
          test -f .env.local || { echo "❌ .env.local not found"; exit 1; }
          echo "✅ Deployment package ready"

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: deployment

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_2B8461954F774159977EC1094BEAAC33 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_71770D985701468CA89CA3C519C9260C }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_B94CD97FA36043F2A4C0CD2AD435B48C }}

      - name: "Deploy to Azure Web App"
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: "otrs-app-backend"
          slot-name: "Production"
          package: .
          startup-command: "node dist/main"
