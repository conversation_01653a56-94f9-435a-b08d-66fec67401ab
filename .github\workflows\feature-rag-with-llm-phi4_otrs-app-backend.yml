# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - otrs-app-backend

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: "22.x"
          cache: "yarn"
          cache-dependency-path: yarn.lock

      - name: Verify Node.js and Yarn versions
        run: |
          node --version
          yarn --version
          echo "Node.js version should be 22.x"

      - name: Install workspace dependencies and build packages
        run: |
          # Install all workspace dependencies (including shared packages)
          yarn install --frozen-lockfile
          # Build shared package first using TypeScript composite builds
          yarn workspace @otrs-ai-powered/shared build
          # Build the backend package with TypeScript references (includes shared)
          yarn workspace @otrs-ai-powered/backend build

      - name: Prepare production dependencies for deployment
        run: |
          # Install only production dependencies (workspace-aware)
          rm -rf node_modules
          yarn install --frozen-lockfile --production

      - name: Copy production environment template
        run: |
          cd packages/backend
          cp .env.production .env.local

      - name: Create deployment package with workspace dependencies
        run: |
          # Create deployment directory
          mkdir -p deployment

          # Copy backend build output
          cp -r packages/backend/dist deployment/

          # Copy environment configuration
          cp packages/backend/.env.local deployment/

          # Create a deployment-specific package.json that includes workspace dependencies
          echo "Creating deployment package.json..."
          cat > deployment/package.json << 'EOF'
          {
            "name": "@otrs-ai-powered/backend-deployment",
            "version": "0.1.0",
            "private": true,
            "main": "dist/main.js",
            "scripts": {
              "start": "node dist/main.js",
              "start:prod": "node dist/main.js"
            },
            "dependencies": {
              "@azure-rest/ai-inference": "^1.0.0-beta.6",
              "@azure/core-auth": "^1.10.0",
              "@azure/core-sse": "^2.3.0",
              "@google/generative-ai": "^0.24.1",
              "@modelcontextprotocol/sdk": "^1.12.0",
              "@nestjs/common": "^10.0.0",
              "@nestjs/config": "^3.0.0",
              "@nestjs/core": "^10.0.0",
              "@nestjs/jwt": "^10.1.0",
              "@nestjs/passport": "^10.0.0",
              "@nestjs/platform-express": "^10.0.0",
              "@nestjs/platform-socket.io": "^10.0.0",
              "@nestjs/websockets": "^10.0.0",
              "axios": "^1.5.0",
              "bcrypt": "^5.1.0",
              "class-transformer": "^0.5.1",
              "class-validator": "^0.14.0",
              "passport": "^0.6.0",
              "passport-jwt": "^4.0.1",
              "passport-local": "^1.0.0",
              "reflect-metadata": "^0.1.13",
              "rxjs": "^7.8.1",
              "uuid": "^9.0.0"
            },
            "engines": {
              "node": ">=22.0.0"
            }
          }
          EOF

          # Copy workspace node_modules (contains all resolved dependencies)
          echo "Copying workspace node_modules to deployment..."
          cp -r node_modules deployment/

          # Copy shared package to ensure it's available for runtime resolution
          echo "=== Ensuring shared package is available for runtime ==="
          mkdir -p deployment/node_modules/@otrs-ai-powered

          # Copy the built shared package
          if [ -d "packages/shared" ]; then
            echo "Copying shared package to deployment..."
            cp -r packages/shared deployment/node_modules/@otrs-ai-powered/shared
            echo "✅ Shared package copied successfully"
          else
            echo "❌ Shared package source not found"
            exit 1
          fi

      - name: Verify deployment package structure
        run: |
          echo "=== Deployment Package Root Structure ==="
          ls -la deployment/
          echo ""
          echo "=== Backend Package.json ==="
          if [ -f "deployment/package.json" ]; then
            echo "✅ Backend package.json found"
            grep -A 5 '"scripts"' deployment/package.json || echo "No scripts section found"
          else
            echo "❌ Backend package.json NOT found"
            exit 1
          fi
          echo ""
          echo "=== Node Modules Structure ==="
          if [ -d "deployment/node_modules" ]; then
            echo "✅ node_modules directory found"
          else
            echo "❌ node_modules directory NOT found"
            exit 1
          fi
          echo ""
          echo "=== Shared Package Verification ==="
          if [ -d "deployment/node_modules/@otrs-ai-powered/shared" ]; then
            echo "✅ Shared package found in deployment"
            if [ -f "deployment/node_modules/@otrs-ai-powered/shared/package.json" ]; then
              echo "✅ Shared package.json found"
            else
              echo "❌ Shared package.json NOT found"
              exit 1
            fi
            if [ -d "deployment/node_modules/@otrs-ai-powered/shared/dist" ]; then
              echo "✅ Shared package dist directory found"
            else
              echo "❌ Shared package dist directory NOT found"
              exit 1
            fi
          else
            echo "❌ Shared package NOT found in deployment"
            exit 1
          fi

      - name: Test module resolution in deployment package
        run: |
          cd deployment
          echo "=== Testing Node.js module resolution ==="

          # Test if Node.js can resolve the shared package
          echo "Testing @otrs-ai-powered/shared module resolution:"
          node -e "
            try {
              const shared = require('@otrs-ai-powered/shared');
              console.log('✅ @otrs-ai-powered/shared resolved successfully');
              console.log('Exported keys:', Object.keys(shared));
            } catch (error) {
              console.error('❌ Failed to resolve @otrs-ai-powered/shared:', error.message);
              process.exit(1);
            }
          "

          # Test basic module loading (without full startup due to missing services in CI)
          echo "Testing main application module loading:"
          node -e "
            try {
              // Test that the main file can be required without errors
              const fs = require('fs');
              if (fs.existsSync('./dist/main.js')) {
                console.log('✅ Main application file exists and is accessible');
              } else {
                console.error('❌ Main application file not found');
                process.exit(1);
              }
            } catch (error) {
              console.error('❌ Failed to access main application:', error.message);
              process.exit(1);
            }
          "

      - name: Verify critical dependencies and files
        run: |
          echo "=== Checking for critical files ==="
          if [ -f "deployment/dist/main.js" ]; then
            echo "✅ main.js found"
          else
            echo "❌ main.js NOT found"
            exit 1
          fi
          if [ -f "deployment/.env.local" ]; then
            echo "✅ Environment file found"
          else
            echo "❌ Environment file NOT found"
            exit 1
          fi
          echo ""
          echo "=== Final deployment package ready ==="
          echo "Deployment package contains:"
          echo "- Backend application: dist/main.js"
          echo "- Environment configuration: .env.local"
          echo "- Node modules with shared package: node_modules/@otrs-ai-powered/shared"
          echo "- Package.json with startup scripts"

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: deployment

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_2B8461954F774159977EC1094BEAAC33 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_71770D985701468CA89CA3C519C9260C }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_B94CD97FA36043F2A4C0CD2AD435B48C }}

      - name: "Deploy to Azure Web App"
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: "otrs-app-backend"
          slot-name: "Production"
          package: .
          startup-command: "node dist/main"
