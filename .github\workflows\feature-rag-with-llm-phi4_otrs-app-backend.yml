# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - otrs-app-backend

on:
  push:
    branches:
      - feature/rag-with-llm-phi4
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: "22.x"
          cache: "yarn"
          cache-dependency-path: yarn.lock

      - name: Verify Node.js and Yarn versions
        run: |
          node --version
          yarn --version
          echo "Node.js version should be 22.x"

      - name: Install workspace dependencies and build packages
        run: |
          # Install all workspace dependencies (including shared packages)
          yarn install --frozen-lockfile
          # Build shared package first (dependency of backend)
          yarn turbo run build --filter=@otrs-ai-powered/shared
          # Build the backend package (depends on shared)
          yarn turbo run build --filter=@otrs-ai-powered/backend

      - name: Prepare production dependencies for deployment
        run: |
          # Install only production dependencies (workspace-aware)
          rm -rf node_modules
          yarn install --frozen-lockfile --production

      - name: Copy production environment template
        run: |
          cd packages/backend
          cp .env.production .env.local

      - name: Create deployment package with workspace dependencies
        run: |
          # Create deployment directory
          mkdir -p deployment

          # Copy backend build output
          cp -r packages/backend/dist deployment/

          # Copy environment configuration
          cp packages/backend/.env.local deployment/

          # Create a deployment-specific package.json that includes workspace dependencies
          echo "Creating deployment package.json..."
          cat > deployment/package.json << 'EOF'
          {
            "name": "@otrs-ai-powered/backend-deployment",
            "version": "0.1.0",
            "private": true,
            "main": "dist/main.js",
            "scripts": {
              "start": "node dist/main.js",
              "start:prod": "node dist/main.js"
            },
            "dependencies": {
              "@azure-rest/ai-inference": "^1.0.0-beta.6",
              "@azure/core-auth": "^1.10.0",
              "@azure/core-sse": "^2.3.0",
              "@google/generative-ai": "^0.24.1",
              "@modelcontextprotocol/sdk": "^1.12.0",
              "@nestjs/common": "^10.0.0",
              "@nestjs/config": "^3.0.0",
              "@nestjs/core": "^10.0.0",
              "@nestjs/jwt": "^10.1.0",
              "@nestjs/passport": "^10.0.0",
              "@nestjs/platform-express": "^10.0.0",
              "@nestjs/platform-socket.io": "^10.0.0",
              "@nestjs/websockets": "^10.0.0",
              "@otrs-ai-powered/shared": "0.1.0",
              "axios": "^1.5.0",
              "bcrypt": "^5.1.0",
              "class-transformer": "^0.5.1",
              "class-validator": "^0.14.0",
              "passport": "^0.6.0",
              "passport-jwt": "^4.0.1",
              "passport-local": "^1.0.0",
              "reflect-metadata": "^0.1.13",
              "rxjs": "^7.8.1",
              "uuid": "^9.0.0"
            },
            "engines": {
              "node": ">=22.0.0"
            }
          }
          EOF

          # Debug: Check workspace node_modules structure before copying
          echo "=== Debugging workspace node_modules structure ==="
          if [ -d "node_modules/@otrs-ai-powered" ]; then
            echo "Found @otrs-ai-powered in workspace node_modules:"
            ls -la node_modules/@otrs-ai-powered/
            if [ -e "node_modules/@otrs-ai-powered/shared" ]; then
              echo "Shared package in workspace node_modules:"
              if [ -L "node_modules/@otrs-ai-powered/shared" ]; then
                echo "  Type: Symlink"
                echo "  Target: $(readlink node_modules/@otrs-ai-powered/shared)"
              elif [ -d "node_modules/@otrs-ai-powered/shared" ]; then
                echo "  Type: Directory"
                ls -la node_modules/@otrs-ai-powered/shared/
              else
                echo "  Type: File"
                file node_modules/@otrs-ai-powered/shared
              fi
            else
              echo "No shared package found in workspace node_modules"
            fi
          else
            echo "No @otrs-ai-powered directory in workspace node_modules"
          fi

          # Copy workspace node_modules (contains all resolved dependencies)
          echo "Copying workspace node_modules to deployment..."
          cp -r node_modules deployment/

          # Explicitly ensure shared package is properly included for runtime resolution
          echo "=== Ensuring shared package is available for runtime ==="

          # Always check and clean the target location after copying node_modules
          echo "Checking target location: deployment/node_modules/@otrs-ai-powered/shared"
          if [ -e "deployment/node_modules/@otrs-ai-powered/shared" ]; then
            echo "Found existing item at deployment/node_modules/@otrs-ai-powered/shared"
            if [ -d "deployment/node_modules/@otrs-ai-powered/shared" ]; then
              echo "Existing item is a directory"
              ls -la deployment/node_modules/@otrs-ai-powered/shared/
            elif [ -L "deployment/node_modules/@otrs-ai-powered/shared" ]; then
              echo "Existing item is a symlink"
              echo "Symlink target: $(readlink deployment/node_modules/@otrs-ai-powered/shared)"
            else
              echo "Existing item is a file"
              file deployment/node_modules/@otrs-ai-powered/shared
            fi
            echo "Removing existing item to avoid conflicts..."
            rm -rf deployment/node_modules/@otrs-ai-powered/shared
            echo "✅ Existing item removed"
          else
            echo "No existing item found at target location"
          fi

          # Verify the target is clean
          if [ -e "deployment/node_modules/@otrs-ai-powered/shared" ]; then
            echo "❌ Target still exists after removal attempt"
            ls -la deployment/node_modules/@otrs-ai-powered/
            exit 1
          else
            echo "✅ Target location is clean"
          fi

          # Create the @otrs-ai-powered directory structure if it doesn't exist
          mkdir -p deployment/node_modules/@otrs-ai-powered

          # Verify shared package source before copying
          echo "=== Verifying shared package source ==="
          if [ -d "packages/shared" ]; then
            echo "✅ Shared package source directory found"
            echo "Shared package source structure:"
            ls -la packages/shared/

            echo "Copying shared package from packages/shared to deployment/node_modules/@otrs-ai-powered/shared"

            # Use rsync for more robust copying that handles existing files better
            if command -v rsync >/dev/null 2>&1; then
              echo "Using rsync for robust copying..."
              echo "Source: packages/shared/"
              echo "Target: deployment/node_modules/@otrs-ai-powered/shared/"

              # Ensure target directory exists
              mkdir -p deployment/node_modules/@otrs-ai-powered/shared

              # Use rsync with options to handle existing files and provide verbose output
              if rsync -av --delete packages/shared/ deployment/node_modules/@otrs-ai-powered/shared/; then
                echo "✅ Shared package copied successfully with rsync"
              else
                echo "❌ Failed to copy shared package with rsync"
                echo "Debugging rsync failure:"
                echo "Source directory exists: $(test -d packages/shared && echo 'YES' || echo 'NO')"
                echo "Target parent exists: $(test -d deployment/node_modules/@otrs-ai-powered && echo 'YES' || echo 'NO')"
                echo "Target directory exists: $(test -d deployment/node_modules/@otrs-ai-powered/shared && echo 'YES' || echo 'NO')"
                if [ -e "deployment/node_modules/@otrs-ai-powered/shared" ]; then
                  echo "Target type: $(file deployment/node_modules/@otrs-ai-powered/shared)"
                fi
                exit 1
              fi
            else
              echo "rsync not available, using cp..."
              echo "Source: packages/shared"
              echo "Target: deployment/node_modules/@otrs-ai-powered/shared"

              # Double-check target is clean before cp
              if [ -e "deployment/node_modules/@otrs-ai-powered/shared" ]; then
                echo "⚠️ Target still exists, removing again..."
                rm -rf deployment/node_modules/@otrs-ai-powered/shared
              fi

              if cp -r packages/shared deployment/node_modules/@otrs-ai-powered/shared; then
                echo "✅ Shared package copied successfully with cp"
              else
                echo "❌ Failed to copy shared package with cp"
                echo "Debugging information:"
                echo "Source exists: $(test -d packages/shared && echo 'YES' || echo 'NO')"
                echo "Target parent exists: $(test -d deployment/node_modules/@otrs-ai-powered && echo 'YES' || echo 'NO')"
                echo "Target exists: $(test -e deployment/node_modules/@otrs-ai-powered/shared && echo 'YES' || echo 'NO')"
                if [ -e "deployment/node_modules/@otrs-ai-powered/shared" ]; then
                  echo "Target type: $(file deployment/node_modules/@otrs-ai-powered/shared)"
                  echo "Target contents:"
                  ls -la deployment/node_modules/@otrs-ai-powered/shared
                fi
                exit 1
              fi
            fi

            # Verify the copy operation was successful
            echo "=== Verifying copy operation success ==="
            if [ -d "deployment/node_modules/@otrs-ai-powered/shared" ]; then
              echo "✅ Shared package directory exists in deployment"

              if [ -f "deployment/node_modules/@otrs-ai-powered/shared/package.json" ]; then
                echo "✅ Shared package.json exists"
              else
                echo "❌ Shared package.json missing"
                exit 1
              fi

              if [ -d "deployment/node_modules/@otrs-ai-powered/shared/dist" ]; then
                echo "✅ Shared package dist directory exists"
                echo "Dist contents:"
                ls -la deployment/node_modules/@otrs-ai-powered/shared/dist/
              else
                echo "❌ Shared package dist directory missing"
                exit 1
              fi

              echo "Final shared package structure:"
              ls -la deployment/node_modules/@otrs-ai-powered/shared/
            else
              echo "❌ Shared package directory not found in deployment after copy"
              echo "Contents of @otrs-ai-powered directory:"
              ls -la deployment/node_modules/@otrs-ai-powered/
              exit 1
            fi
          else
            echo "❌ Shared package source not found"
            exit 1
          fi

          # Verify the shared package is now accessible in deployment
          if [ -f "deployment/node_modules/@otrs-ai-powered/shared/package.json" ]; then
            echo "✅ Shared package properly placed in deployment node_modules"
            echo "Shared package.json content:"
            cat deployment/node_modules/@otrs-ai-powered/shared/package.json
          else
            echo "❌ Shared package NOT properly placed in deployment"
            exit 1
          fi

      - name: Verify deployment package structure
        run: |
          echo "=== Deployment Package Root Structure ==="
          ls -la deployment/
          echo ""
          echo "=== Backend Package.json ==="
          if [ -f "deployment/package.json" ]; then
            echo "✅ Backend package.json found"
            grep -A 5 '"scripts"' deployment/package.json || echo "No scripts section found"
          else
            echo "❌ Backend package.json NOT found"
            exit 1
          fi
          echo ""
          echo "=== Node Modules Structure ==="
          if [ -d "deployment/node_modules" ]; then
            echo "✅ node_modules directory found"
            echo "Top-level packages in node_modules:"
            ls deployment/node_modules/ | head -10
          else
            echo "❌ node_modules directory NOT found"
            exit 1
          fi
          echo ""
          echo "=== Workspace Dependencies Verification ==="
          if [ -d "deployment/node_modules/@otrs-ai-powered" ]; then
            echo "✅ @otrs-ai-powered workspace packages found"
            ls -la deployment/node_modules/@otrs-ai-powered/

            if [ -d "deployment/node_modules/@otrs-ai-powered/shared" ]; then
              echo "✅ Shared package found in workspace"
              echo "Shared package structure:"
              ls -la deployment/node_modules/@otrs-ai-powered/shared/

              if [ -f "deployment/node_modules/@otrs-ai-powered/shared/package.json" ]; then
                echo "✅ Shared package.json found"
                echo "Shared package main entry:"
                grep -E '"main"|"types"|"module"' deployment/node_modules/@otrs-ai-powered/shared/package.json
              else
                echo "❌ Shared package.json NOT found"
                exit 1
              fi

              if [ -d "deployment/node_modules/@otrs-ai-powered/shared/dist" ]; then
                echo "✅ Shared package dist directory found"
                ls -la deployment/node_modules/@otrs-ai-powered/shared/dist/
              else
                echo "❌ Shared package dist directory NOT found"
                exit 1
              fi
            else
              echo "❌ Shared package NOT found in workspace"
              exit 1
            fi
          else
            echo "❌ @otrs-ai-powered workspace packages NOT found"
            exit 1
          fi

      - name: Test module resolution in deployment package
        run: |
          cd deployment
          echo "=== Testing Node.js module resolution ==="

          # Test if Node.js can resolve the shared package
          echo "Testing @otrs-ai-powered/shared module resolution:"
          node -e "
            try {
              const shared = require('@otrs-ai-powered/shared');
              console.log('✅ @otrs-ai-powered/shared resolved successfully');
              console.log('Exported keys:', Object.keys(shared));
            } catch (error) {
              console.error('❌ Failed to resolve @otrs-ai-powered/shared:', error.message);
              process.exit(1);
            }
          "

          # Test if the main application file can be loaded
          echo "Testing main application file:"
          node -e "
            try {
              require('./dist/main.js');
              console.log('✅ Main application file loaded successfully');
            } catch (error) {
              console.error('❌ Failed to load main application:', error.message);
              console.error('Error stack:', error.stack);
              process.exit(1);
            }
          " || echo "⚠️ Main application test failed (expected in CI environment)"

      - name: Verify critical dependencies and files
        run: |
          echo "=== Checking for critical NestJS dependencies ==="
          if [ -d "deployment/node_modules/@nestjs/core" ]; then
            echo "✅ @nestjs/core found"
          else
            echo "❌ @nestjs/core NOT found"
            exit 1
          fi
          if [ -d "deployment/node_modules/@nestjs/common" ]; then
            echo "✅ @nestjs/common found"
          else
            echo "❌ @nestjs/common NOT found"
            exit 1
          fi
          if [ -f "deployment/dist/main.js" ]; then
            echo "✅ main.js found"
          else
            echo "❌ main.js NOT found"
            exit 1
          fi
          echo ""
          echo "=== Environment file preview ==="
          head -5 deployment/.env.local

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: deployment

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_2B8461954F774159977EC1094BEAAC33 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_71770D985701468CA89CA3C519C9260C }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_B94CD97FA36043F2A4C0CD2AD435B48C }}

      - name: "Deploy to Azure Web App"
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: "otrs-app-backend"
          slot-name: "Production"
          package: .
          startup-command: "node dist/main"
