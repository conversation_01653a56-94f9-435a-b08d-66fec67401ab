# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions
#
# Required GitHub Secrets:
# - JWT_SECRET: Secret key for JWT token signing
# - LLM_API_KEY: API key for LLM provider (Azure AI)
# - LLM_BASE_URL: Base URL for LLM provider endpoint
# - RAG_SYSTEM_URL: URL for RAG system microservice
# - MCP_SERVER_URL: URL for MCP server
# - CORS_ORIGIN: Allowed CORS origin for production frontend
# - AZUREAPPSERVICE_CLIENTID_*: Azure service principal client ID
# - AZUREAPPSERVICE_TENANTID_*: Azure service principal tenant ID
# - AZUREAPPSERVICE_SUBSCRIPTIONID_*: Azure subscription ID

name: Build and deploy Node.js app to Azure Web App - otrs-app-backend

on:
  push:
    branches:
      - feature/rag-with-llm-phi4
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: "22.x"
          cache: "yarn"
          cache-dependency-path: yarn.lock

      - name: Build and prepare backend package
        run: |
          yarn workspace @otrs-ai-powered/backend install
          yarn workspace @otrs-ai-powered/backend build

      - name: Configure production environment with secrets
        run: |
          # Copy production template
          cp packages/backend/.env.production packages/backend/.env.local

          # Replace placeholder values with actual secrets
          sed -i "s/\${JWT_SECRET}/${{ secrets.JWT_SECRET }}/g" packages/backend/.env.local
          sed -i "s/\${LLM_API_KEY}/${{ secrets.LLM_API_KEY }}/g" packages/backend/.env.local
          sed -i "s/\${LLM_BASE_URL}/${{ secrets.LLM_BASE_URL }}/g" packages/backend/.env.local
          sed -i "s|\${RAG_SYSTEM_URL}|${{ secrets.RAG_SYSTEM_URL }}|g" packages/backend/.env.local
          sed -i "s|\${MCP_SERVER_URL}|${{ secrets.MCP_SERVER_URL }}|g" packages/backend/.env.local
          sed -i "s|\${CORS_ORIGIN}|${{ secrets.CORS_ORIGIN }}|g" packages/backend/.env.local

          # Verify environment file was created
          echo "✅ Environment file configured with secrets"
          echo "Environment variables set:"
          grep -E "^[A-Z_]+=.+" packages/backend/.env.local | grep -v "SECRET\|KEY" | head -10

      - name: Verify backend package
        run: |
          cd packages/backend

          # Test shared package resolution
          node -e "
            try {
              const shared = require('@otrs-ai-powered/shared');
              console.log('✅ Shared package resolved successfully');
            } catch (error) {
              console.error('❌ Failed to resolve shared package:', error.message);
              process.exit(1);
            }
          "

          # Verify critical files exist
          test -f dist/src/main.js || { echo "❌ main.js not found"; exit 1; }
          test -f .env.local || { echo "❌ .env.local not found"; exit 1; }

          # Verify environment variables were replaced (check for absence of placeholders)
          if grep -q "\${" .env.local; then
            echo "❌ Environment file still contains unreplaced placeholders:"
            grep "\${" .env.local
            exit 1
          else
            echo "✅ All environment placeholders replaced successfully"
          fi

          echo "✅ Backend package ready for deployment"

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: .

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_2B8461954F774159977EC1094BEAAC33 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_71770D985701468CA89CA3C519C9260C }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_B94CD97FA36043F2A4C0CD2AD435B48C }}

      - name: "Deploy to Azure Web App"
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: "otrs-app-backend"
          slot-name: "Production"
          package: .
          startup-command: "node packages/backend/dist/src/main"
