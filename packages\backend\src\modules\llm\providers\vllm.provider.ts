import { Injectable, Logger } from '@nestjs/common';
import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import { ILlmProvider, LlmProviderConfig, LlmCompletionResponse } from '../interfaces/llm-provider.interface';
import {
  VllmConfigurationError,
  VllmErrorFactory,
  VllmErrorHandler,
  VllmChatFormatError,
  VllmError,
} from '../errors/vllm-errors';
import {
  VllmProviderConfig,
  VllmChatCompletionRequest,
  VllmChatCompletionResponse,
} from '../types/vllm.types';

@Injectable()
export class VllmProvider implements ILlmProvider {
  private readonly logger = new Logger(VllmProvider.name);
  private readonly config: VllmProviderConfig;

  constructor(config: LlmProviderConfig) {
    if (!config.baseUrl) {
      throw new VllmConfigurationError('baseUrl is required for vLLM provider', { config });
    }
    if (!config.apiKey) {
      throw new VllmConfigurationError('apiKey is required for vLLM provider', { config });
    }
    if (!config.model) {
      throw new VllmConfigurationError('model is required for vLLM provider', { config });
    }
    this.config = config as VllmProviderConfig;
  }

  async generateCompletion(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<LlmCompletionResponse> {
    try {
      this.logger.debug(`Generating completion with ${messages.length} messages`);

      // Validate input
      if (!messages || messages.length === 0) {
        throw new VllmChatFormatError('Messages array cannot be empty');
      }

      // Convert messages to Phi-4 chat format
      const formattedPrompt = this.convertToPhi4ChatFormat(messages, tools);

      // Create vLLM request
      const request: VllmChatCompletionRequest = {
        model: this.config.model,
        messages: [{ role: 'user', content: formattedPrompt }],
        temperature: this.config.temperature ?? 0.7,
        max_tokens: this.config.maxTokens ?? 2000,
      };

      // Make HTTP request to vLLM server with retry logic
      const response = await this.makeRequestWithRetry('/v1/chat/completions', request);

      // Parse response and extract content/tool calls
      return this.parseVllmResponse(response);
    } catch (error) {
      this.logger.error('Error generating completion:', error);

      // Re-throw vLLM errors as-is
      if (error instanceof VllmError) {
        throw error;
      }

      // Convert other errors to vLLM errors
      if (error instanceof Error) {
        throw VllmErrorFactory.createFromNetworkError(error, {
          messages: messages.length,
          hasTools: !!tools?.length
        });
      }

      throw VllmErrorFactory.createFromNetworkError(new Error('Unknown error during completion generation'));
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Basic configuration validation
      if (!this.config.apiKey) {
        this.logger.error('vLLM API key is missing');
        return false;
      }

      if (!this.config.baseUrl) {
        this.logger.error('vLLM base URL is missing');
        return false;
      }

      if (!this.config.model) {
        this.logger.error('vLLM model is missing');
        return false;
      }

      // Test the API with a simple request
      const testRequest: VllmChatCompletionRequest = {
        model: this.config.model,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
        temperature: 0.1,
      };

      await this.makeRequest('/v1/chat/completions', testRequest);
      this.logger.debug('vLLM API validation successful');
      return true;
    } catch (error) {
      if (error instanceof VllmError) {
        this.logger.error(`vLLM API validation failed: ${error.message}`, {
          code: error.code,
          statusCode: error.statusCode,
        });
      } else {
        this.logger.error('vLLM API validation failed with unexpected error:', error);
      }
      return false;
    }
  }

  getProviderName(): string {
    return 'vllm';
  }

  getModel(): string {
    return this.config.model;
  }

  /**
   * Convert messages to Phi-4 chat format
   */
  private convertToPhi4ChatFormat(messages: ChatMessage[], tools?: Tool[]): string {
    try {
      let prompt = '';

      // Determine if this is a standard chat or MCP/function calling scenario
      const hasMcpTools = tools && tools.length > 0;

      // Add system message
      const systemMessage = messages.find(msg => msg.role === 'system');
      let systemContent = systemMessage?.content ?? 'You are a helpful assistant.';

      if (hasMcpTools) {
        // MCP/Function calling format - enhance system message for tool usage
        if (!systemContent.toLowerCase().includes('tool')) {
          systemContent += ' You have access to some tools.';
        }

        prompt += `<|system|>${systemContent}<|tool|>`;

        // Convert tools to the expected MCP format
        const toolsFormatted = this.convertToolsToMcpFormat(tools);
        prompt += JSON.stringify(toolsFormatted);
        prompt += '<|/tool|><|end|>';
      } else {
        // Standard chat format
        prompt += `<|system|>${systemContent}<|end|>`;
      }

      // Add conversation messages (excluding system message as it's already handled)
      const conversationMessages = messages.filter(msg => msg.role !== 'system');

      for (const message of conversationMessages) {
        if (message.role === 'user') {
          prompt += `<|user|>${this.sanitizeContent(message.content)}<|end|>`;
        } else if (message.role === 'assistant') {
          prompt += `<|assistant|>${this.sanitizeContent(message.content)}<|end|>`;
        }
        // Note: Tool result messages are handled as system messages in the conversation flow
      }

      // Add assistant prompt for response
      prompt += '<|assistant|>';

      this.logger.debug(`Generated Phi-4 prompt (${prompt.length} chars): ${prompt.substring(0, 200)}...`);
      return prompt;
    } catch (error) {
      this.logger.error('Error converting to Phi-4 chat format:', error);
      throw new VllmChatFormatError('Failed to convert messages to Phi-4 format', {
        messagesCount: messages.length,
        hasTools: !!tools?.length,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Convert tools to MCP format for Phi-4
   */
  private convertToolsToMcpFormat(tools: Tool[]): Array<{
    name: string;
    description: string;
    parameters: Record<string, any>;
  }> {
    return tools.map(tool => {
      // Validate tool structure
      if (!tool.name || !tool.description) {
        throw new VllmChatFormatError(`Invalid tool structure: missing name or description`, { tool });
      }

      return {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters || {},
      };
    });
  }

  /**
   * Sanitize content to prevent format injection
   */
  private sanitizeContent(content: string): string {
    if (!content) return '';

    // Escape Phi-4 special tokens to prevent format injection
    return content
      .replace(/<\|system\|>/g, '&lt;|system|&gt;')
      .replace(/<\|user\|>/g, '&lt;|user|&gt;')
      .replace(/<\|assistant\|>/g, '&lt;|assistant|&gt;')
      .replace(/<\|tool\|>/g, '&lt;|tool|&gt;')
      .replace(/<\|\/tool\|>/g, '&lt;|/tool|&gt;')
      .replace(/<\|end\|>/g, '&lt;|end|&gt;');
  }

  /**
   * Make HTTP request to vLLM server with retry logic
   */
  private async makeRequestWithRetry(endpoint: string, data: any, maxRetries = 3): Promise<VllmChatCompletionResponse> {
    let lastError: VllmError | undefined;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.makeRequest(endpoint, data);
      } catch (error) {
        const vllmError = error instanceof VllmError ? error : VllmErrorFactory.createFromNetworkError(error as Error);
        lastError = vllmError;

        if (!VllmErrorHandler.isRetryable(vllmError) || attempt === maxRetries) {
          throw vllmError;
        }

        const delay = VllmErrorHandler.getRetryDelay(vllmError, attempt);
        this.logger.warn(`Request failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, vllmError.message);

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Make HTTP request to vLLM server
   */
  private async makeRequest(endpoint: string, data: any): Promise<VllmChatCompletionResponse> {
    const url = `${this.config.baseUrl.replace(/\/$/, '')}${endpoint}`;

    this.logger.debug(`Making request to vLLM server: ${url}`);

    try {
      const controller = new AbortController();
      const timeoutMs = this.config.timeout ?? 30000;
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
          'User-Agent': 'OTRS-AI-Powered/1.0',
        },
        body: JSON.stringify(data),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorBody: string | undefined;

        try {
          errorBody = await response.text();
        } catch (parseError) {
          this.logger.debug('Could not parse error response body', parseError);
        }

        throw VllmErrorFactory.createFromHttpResponse(
          response.status,
          response.statusText,
          errorBody,
          { url, endpoint }
        );
      }

      try {
        const responseData = await response.json();
        this.logger.debug('Received successful response from vLLM server');
        return responseData;
      } catch (parseError) {
        const errorBody = await response.text().catch(() => 'Unable to read response');
        throw VllmErrorFactory.createFromParsingError(
          parseError as Error,
          errorBody,
          { url, endpoint }
        );
      }
    } catch (error) {
      if (error instanceof VllmError) {
        throw error;
      }

      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutMs = this.config.timeout ?? 30000;
        throw VllmErrorFactory.createFromNetworkError(error, { url, endpoint, timeoutMs });
      }

      throw VllmErrorFactory.createFromNetworkError(error as Error, { url, endpoint });
    }
  }

  /**
   * Parse vLLM response and extract content/tool calls
   */
  private parseVllmResponse(response: VllmChatCompletionResponse): LlmCompletionResponse {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('No choices in vLLM response');
    }

    const choice = response.choices[0];
    const message = choice.message;
    let content = typeof message.content === 'string' ? message.content : '';

    // Extract tool calls if present (either from OpenAI format or from content parsing)
    const toolCalls: ToolCall[] = [];

    // First, check for OpenAI-compatible tool calls
    if (message.tool_calls) {
      for (const toolCall of message.tool_calls) {
        try {
          const args = JSON.parse(toolCall.function.arguments);
          toolCalls.push({
            toolId: toolCall.id,
            toolName: toolCall.function.name,
            arguments: args,
          });
        } catch (error) {
          this.logger.warn(`Failed to parse tool call arguments: ${error}`);
        }
      }
    } else if (typeof message.content === 'string') {
      // Parse tool calls from Phi-4 response content if not in OpenAI format
      const parsedToolCalls = this.parseToolCallsFromContent(message.content);
      toolCalls.push(...parsedToolCalls);

      // Remove tool call syntax from content if tool calls were found
      if (parsedToolCalls.length > 0) {
        content = this.cleanContentFromToolCalls(message.content);
      }
    }

    return {
      content,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined,
    };
  }

  /**
   * Parse tool calls from Phi-4 response content
   */
  private parseToolCallsFromContent(content: string): ToolCall[] {
    const toolCalls: ToolCall[] = [];

    try {
      // Look for various function call patterns in the response
      const patterns = [
        // Pattern 1: function_name(arg1="value1", arg2="value2")
        /(\w+)\(([^)]+)\)/g,
        // Pattern 2: {"function": "function_name", "arguments": {...}}
        /\{"function":\s*"([^"]+)",\s*"arguments":\s*(\{[^}]*\})\}/g,
        // Pattern 3: function_name: {...}
        /(\w+):\s*(\{[^}]*\})/g,
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          try {
            const toolCall = this.parseIndividualToolCall(match, pattern);
            if (toolCall) {
              toolCalls.push(toolCall);
            }
          } catch (error) {
            this.logger.debug(`Could not parse potential tool call: ${match[0]}`, error);
          }
        }
      }

      // Remove duplicates based on function name and arguments
      return this.deduplicateToolCalls(toolCalls);
    } catch (error) {
      this.logger.warn('Error parsing tool calls from content:', error);
      return [];
    }
  }

  /**
   * Parse individual tool call from regex match
   */
  private parseIndividualToolCall(match: RegExpExecArray, pattern: RegExp): ToolCall | null {
    const functionName = match[1];
    const argsString = match[2];

    if (!functionName || !argsString) {
      return null;
    }

    let args: Record<string, any> = {};

    try {
      if (argsString.startsWith('{') && argsString.endsWith('}')) {
        // JSON format
        args = JSON.parse(argsString);
      } else {
        // Key-value format: arg1="value1", arg2="value2"
        args = this.parseKeyValueArguments(argsString);
      }
    } catch (error) {
      this.logger.debug(`Failed to parse arguments for ${functionName}: ${argsString}`, error);
      return null;
    }

    return {
      toolId: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      toolName: functionName,
      arguments: args,
    };
  }

  /**
   * Parse key-value arguments format
   */
  private parseKeyValueArguments(argsString: string): Record<string, any> {
    const args: Record<string, any> = {};

    // Split by comma, but respect quoted strings
    const argPairs = argsString.match(/(\w+)=("[^"]*"|'[^']*'|\S+)/g) || [];

    for (const pair of argPairs) {
      const [key, value] = pair.split('=', 2);
      if (key && value) {
        // Remove quotes if present
        let cleanValue: any = value.replace(/^["']|["']$/g, '');

        // Try to parse as number or boolean
        if (cleanValue === 'true') cleanValue = true;
        else if (cleanValue === 'false') cleanValue = false;
        else if (!isNaN(Number(cleanValue)) && cleanValue !== '') cleanValue = Number(cleanValue);

        args[key.trim()] = cleanValue;
      }
    }

    return args;
  }

  /**
   * Remove duplicate tool calls
   */
  private deduplicateToolCalls(toolCalls: ToolCall[]): ToolCall[] {
    const seen = new Set<string>();
    return toolCalls.filter(toolCall => {
      const signature = `${toolCall.toolName}:${JSON.stringify(toolCall.arguments)}`;
      if (seen.has(signature)) {
        return false;
      }
      seen.add(signature);
      return true;
    });
  }

  /**
   * Clean tool call syntax from content
   */
  private cleanContentFromToolCalls(content: string): string {
    // Remove function call patterns from content
    return content.replace(/\w+\(.*?\)/g, '').trim();
  }
}
